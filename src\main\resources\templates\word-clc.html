<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="application/msword; charset=utf-8"/>
    <title>toWord</title>
    <style type="text/css">
        .bg {
            font-size: 14.5px;
            font-weight: bold;
            /*color: #000;*/
            /*background-color: #559e68;*/
        }


        table {
            /*border-width: 1px;*/
            border-style: solid;
            /*border-color: black;*/
            table-layout: fixed;
        }

        tr {
            height: 32px;
            font-size: 12px;
        }

        td {
            padding-left: 10px;
            /*border-width: 1px;*/
            border-style: solid;
            /*border-color: black;*/
            height: 32px;
            overflow: hidden;
            word-break: break-all;
            word-wrap: break-word;
            font-size: 14.5px;
        }

        .bg td {
            font-size: 14.5px;
        }

        tr td {
            font-size: 14.5px;
        }

        .specialHeight {
            height: 40px;
        }

        .first_title {
            /*height: 60px;*/
            /*line-height: 60px;*/
            /*margin: 0;*/
            /*font-weight: bold;*/
            /*font-size: 21px;*/
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px; /* 使用像素（px）作为单位 */
        }

        .second_title {
            /*height: 40px;*/
            /*line-height: 40px;*/
            /*margin: 0;*/
            /*font-size: 18.5px;*/
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px; /* 使用像素（px）作为单位 */
        }

        .doc_title {
            /*font-size: 42.5px;*/
            /*text-align: center;*/
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px; /* 使用像素（px）作为单位 */
        }

        .download_btn {
            float: right;
        }

        body {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px; /* 使用像素（px）作为单位 */
        }
    </style>
</head>

<body>
<div style="width:800px; margin: 0 auto">
    <div>
        <h4 class="doc_title" th:text="${info.title +'（'+ info.version +'）'}"></h4>
        <a class="download_btn" th:if="${download == 1}" th:href="${'/downloadWord?url='+ url}">下载文档</a>
        <br>
    </div>
    <div th:each="tableMap:${tableMap}" style="margin-bottom:20px;">
        <!--这个是类的说明-->
        <h4 class="first_title" th:text="${tableMap.key}"></h4>
        <div th:each="table,tableStat:${tableMap.value}">
            <!--这个是每个请求的说明，方便生成文档后进行整理-->
            <h4 class="second_title" th:text="${table.description}"></h4>
            <h5 class="second_title" >功能点描述</h5>
            <div th:text="${table.description}"></div>
            <h5 class="second_title" >输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4" th:text="${table.description}"></td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4" th:text="${table.basePath}"></td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4" >深铁app/深铁小程序/港铁小程序</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4" th:text="${#strings.toUpperCase(table.requestType)}"></td>
                    <!--                    <td colspan="4" >http POST</td>-->
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4" th:text="${table.url}"></td>
                </tr>
<!--                <tr>-->
<!--                    <td>请求方式</td>-->
<!--                    <td colspan="4" th:text="${#strings.toUpperCase(table.requestType)}"></td>-->
<!--                </tr>-->
                <tr>
                    <td>超时时长</td>
                    <td colspan="4" >5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4" >请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4" >响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">

                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>

                <th:block th:each="request, c:${table.requestList}">
                    <tr>
                        <td align="left" th:text="${c.count} + '.' + ${request.name}"></td>
                        <td th:text="${request.type}"></td>
                        <td th:text="${request.paramType}"></td>
                        <td th:if="${request.require}" th:text="Y"></td>
                        <td th:if="${!request.require}" th:text="N"></td>
                        <td th:text="${request.remark}"></td>
                        <!--                        <td th:if="${request.modelAttr}" th:text="asdfagadfg"></td>-->
                    </tr>
                    <th:block th:if="${request.modelAttr}">
                        <tbody th:include="this::request(${request.modelAttr.properties},${c.count} + '.', 1)"/>
                    </th:block>
                </th:block>

<!--                <tr class="bg">-->
<!--                    <td>状态码</td>-->
<!--                    <td colspan="2">描述</td>-->
<!--                    <td colspan="2">说明</td>-->
<!--                </tr>-->

<!--                <tr th:each="response:${table.responseList}">-->
<!--                    <td th:text="${response.name}"></td>-->
<!--                    <td colspan="2" th:text="${response.description}"></td>-->
<!--                    <td colspan="2" th:text="${response.remark}"></td>-->
<!--                </tr>-->
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <!--                    <td colspan="4" class="json-display" th:text="${table.requestParam}"></td>-->
                    <!--                    <td colspan="4" th:text="${table.requestParam}"></td>-->
                    <td colspan="4"><pre th:text="${table.requestParam}"></pre></td>
                </tr>
            </table>
            <br>
            <br>
            <h5 class="second_title" >输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>

                <!--               对返回参数 递归生成行-->
                <tbody th:include="this::response(${table.modelAttr.properties},'', 1)"/>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
<!--                    <td colspan="4" th:text="${table.responseParam}"></td>-->
                    <td colspan="4"><pre th:text="${table.responseParam}"></pre></td>
                </tr>

            </table>
            <br>
            <br>
        </div>
    </div>
</div>

<th:block th:fragment="request(properties,count, lv)">
    <th:block th:each="p,c : ${properties}">
        <tr>
            <td align="left" th:text="${count} + '' + ${c.count} + '.' + ${p.name}"
                th:style="|padding-left:${10*lv}px|"></td>
            <td th:text="${p.type}"></td>
            <td></td>
            <td th:if="${p.require}" th:text="Y"></td>
            <td th:if="${!p.require}" th:text="N"></td>
            <td th:text="${p.description}"></td>
        </tr>
        <th:block th:unless="${#lists.isEmpty(p.properties)}"
                  th:include="this::request(${p.properties},${count} + '' + ${c.count} + '.',${lv+1})"/>
    </th:block>
</th:block>

<th:block th:fragment="response(properties,count, lv)">
    <th:block th:each="p,c : ${properties}">
        <tr>
            <td align="left" th:text="${count} + '' + ${c.count} + '.' + ${p.name}"
                th:style="|padding-left:${10*lv}px|"></td>
            <td colspan="2" th:text="${p.type}"></td>
            <td colspan="2" th:text="${p.description}"></td>
        </tr>
        <th:block th:unless="${#lists.isEmpty(p.properties)}"
                  th:include="this::response(${p.properties},${count} + '' + ${c.count} + '.',${lv+1})"/>
    </th:block>
</th:block>
</body>
</html>
