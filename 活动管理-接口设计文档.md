# 活动管理系统接口设计文档

## **1.1** ***\*活动类别管理模块\****

#### *********** ***\*分页查询活动类别接口\****

##### *********.1** ***\*接口功能\****

管理后台分页查询活动类别列表，支持按名称和时间范围筛选。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 分页查询活动类别列表，支持按名称和时间范围筛选     |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityCategory/page |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名      | 类型   | 备注                                  | 是否必填 |
| ------------ | ------------- | ------ | ------------------------------------- | -------- |
| 页码         | pageNum       | Int    | 默认1                                 | 否       |
| 页大小       | pageSize      | Int    | 默认10                                | 否       |
| 活动类别名称 | categoryName  | String | 支持模糊查询                          | 否       |
| 创建开始时间 | startTime     | String | 格式：yyyy-MM-dd HH:mm:ss             | 否       |
| 创建结束时间 | endTime       | String | 格式：yyyy-MM-dd HH:mm:ss             | 否       |

请求示例：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "categoryName": "摄影",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名     | 类型   | 备注                             | 是否必填 |
| ------------ | ------------ | ------ | -------------------------------- | -------- |
| 状态码       | resultCode   | String | 业务响应码                       | 是       |
| 提示         | resultMsg    | String | 业务响应信息                     | 是       |
| 数据         | data         | Object | 分页数据                         | 是       |
| 总记录数     | total        | Long   | 总记录数                         | 是       |
| 数据列表     | records      | Array  | 活动类别列表                     | 是       |
| 主键ID       | id           | Int    | 主键ID                           | 是       |
| 活动类别名称 | categoryName | String | 活动类别名称                     | 是       |
| 表单配置JSON | formConfig   | String | 表单配置JSON                     | 是       |
| 创建时间     | createdTime  | String | 创建时间                         | 是       |
| 更新时间     | updatedTime  | String | 更新时间                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 2,
    "records": [
      {
        "id": 1,
        "categoryName": "摄影比赛",
        "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*新增活动类别接口\****

##### *********.1** ***\*接口功能\****

管理后台新增活动类别，包含动态表单配置。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 新增活动类别，包含动态表单配置                     |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityCategory/add |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名     | 类型   | 备注                                                         | 是否必填 |
| ------------ | ------------ | ------ | ------------------------------------------------------------ | -------- |
| 活动类别名称 | categoryName | String | 活动类别名称                                                 | 是       |
| 表单配置JSON | formConfig   | String | 表单配置JSON，每次修改必须传递完整的JSON配置不支持部分更新。inputType枚举值：input(文本输入框)、file(文件/图片上传支持多个逗号分割MinIO的URL)、datetime(时间组件)、date(日期组件)、textarea(多行文本框) | 是       |

请求示例：
```json
{
  "categoryName": "摄影比赛",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50},{\"fieldName\":\"作品图片\",\"required\":true,\"inputType\":\"file\",\"maxLength\":500}]"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*修改活动类别接口\****

##### *********.1** ***\*接口功能\****

管理后台修改活动类别信息，有子活动的类别不支持修改表单配置。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 修改活动类别信息，有子活动的类别不支持修改表单配置 |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityCategory/update |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名     | 类型   | 备注                                                         | 是否必填 |
| ------------ | ------------ | ------ | ------------------------------------------------------------ | -------- |
| 主键ID       | id           | Int    | 主键ID                                                       | 是       |
| 活动类别名称 | categoryName | String | 活动类别名称                                                 | 是       |
| 表单配置JSON | formConfig   | String | 表单配置JSON，每次修改必须传递完整的JSON配置不支持部分更新。inputType枚举值：input(文本输入框)、file(文件/图片上传支持多个逗号分割MinIO的URL)、datetime(时间组件)、date(日期组件)、textarea(多行文本框) | 否       |

请求示例：
```json
{
  "id": 1,
  "categoryName": "摄影比赛",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*删除活动类别接口\****

##### *********.1** ***\*接口功能\****

管理后台删除活动类别，只有没有关联活动的类别才能删除。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 删除活动类别，只有没有关联活动的类别才能删除       |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityCategory/delete |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型 | 备注   | 是否必填 |
| -------- | -------- | ---- | ------ | -------- |
| 主键ID   | id       | Int  | 主键ID | 是       |

请求示例：
```json
{
  "id": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*活动类别详情接口\****

##### *********.1** ***\*接口功能\****

管理后台查询活动类别详情信息。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 查询活动类别详情信息                               |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityCategory/detail |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型 | 备注   | 是否必填 |
| -------- | -------- | ---- | ------ | -------- |
| 主键ID   | id       | Int  | 主键ID | 是       |

请求示例：
```json
{
  "id": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名     | 类型   | 备注                             | 是否必填 |
| ------------ | ------------ | ------ | -------------------------------- | -------- |
| 状态码       | resultCode   | String | 业务响应码                       | 是       |
| 提示         | resultMsg    | String | 业务响应信息                     | 是       |
| 数据         | data         | Object | 活动类别详情                     | 是       |
| 主键ID       | id           | Int    | 主键ID                           | 是       |
| 活动类别名称 | categoryName | String | 活动类别名称                     | 是       |
| 表单配置JSON | formConfig   | String | 表单配置JSON                     | 是       |
| 创建时间     | createdTime  | String | 创建时间                         | 是       |
| 更新时间     | updatedTime  | String | 更新时间                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "categoryName": "摄影比赛",
    "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
    "createdTime": "2024-01-01 10:00:00",
    "updatedTime": "2024-01-01 10:00:00"
  }
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*列表所有活动类别接口\****

##### *********.1** ***\*接口功能\****

管理后台查询所有活动类别列表，用于下拉选择。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 查询所有活动类别列表，用于下拉选择                 |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityCategory/listAll |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

无

请求示例：
```json
{}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名     | 类型   | 备注                             | 是否必填 |
| ------------ | ------------ | ------ | -------------------------------- | -------- |
| 状态码       | resultCode   | String | 业务响应码                       | 是       |
| 提示         | resultMsg    | String | 业务响应信息                     | 是       |
| 数据         | data         | Array  | 活动类别列表                     | 是       |
| 主键ID       | id           | Int    | 主键ID                           | 是       |
| 活动类别名称 | categoryName | String | 活动类别名称                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryName": "摄影比赛"
    },
    {
      "id": 2,
      "categoryName": "文艺演出"
    }
  ]
}
```

##### *********.5** ***\*处理流程\****

无

## **1.2** ***\*活动内容管理模块\****

#### *********** ***\*分页查询活动内容接口\****

##### *********.1** ***\*接口功能\****

管理后台分页查询活动内容列表，支持按活动名称、类别、状态和时间范围筛选。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 分页查询活动内容列表，支持按活动名称、类别、状态和时间范围筛选 |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityContent/page |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名      | 类型   | 备注                                  | 是否必填 |
| ------------ | ------------- | ------ | ------------------------------------- | -------- |
| 页码         | pageNum       | Int    | 默认1                                 | 否       |
| 页大小       | pageSize      | Int    | 默认10                                | 否       |
| 活动名称     | activityName  | String | 支持模糊查询                          | 否       |
| 活动类别ID   | categoryId    | Int    | 活动类别ID                            | 否       |
| 活动状态     | activityStatus| String | 枚举值：10(未发布)、20(待开始)、30(进行中)、40(已作废)、50(已结束) | 否       |
| 创建开始时间 | startTime     | String | 格式：yyyy-MM-dd HH:mm:ss             | 否       |
| 创建结束时间 | endTime       | String | 格式：yyyy-MM-dd HH:mm:ss             | 否       |

请求示例：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "activityName": "摄影",
  "categoryId": 1,
  "activityStatus": "20",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 分页数据                         | 是       |
| 总记录数     | total            | Long   | 总记录数                         | 是       |
| 数据列表     | records          | Array  | 活动内容列表                     | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动类别ID   | categoryId       | Int    | 活动类别ID                       | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动描述     | activityDesc     | String | 活动描述                         | 是       |
| 活动图片URL  | activityImageUrl | String | 活动图片URL                      | 是       |
| 活动开始时间 | startTime        | String | 活动开始时间                     | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间                     | 是       |
| 活动状态     | activityStatus   | String | 活动状态                         | 是       |
| 报名人数     | registrationCount| Int    | 报名人数                         | 是       |
| 创建时间     | createdTime      | String | 创建时间                         | 是       |
| 更新时间     | updatedTime      | String | 更新时间                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryId": 1,
        "categoryName": "摄影比赛",
        "activityName": "春季摄影大赛",
        "activityDesc": "欢迎参加春季摄影大赛",
        "activityImageUrl": "http://minio.example.com/activity1.jpg",
        "startTime": "2024-03-01 09:00:00",
        "endTime": "2024-03-31 18:00:00",
        "activityStatus": "20",
        "registrationCount": 15,
        "createdTime": "2024-02-01 10:00:00",
        "updatedTime": "2024-02-01 10:00:00"
      }
    ]
  }
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*新增活动内容接口\****

##### *********.1** ***\*接口功能\****

管理后台新增活动内容。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 新增活动内容                                       |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityContent/add |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名         | 类型   | 备注                                  | 是否必填 |
| ------------ | ---------------- | ------ | ------------------------------------- | -------- |
| 活动类别ID   | categoryId       | Int    | 活动类别ID                            | 是       |
| 活动名称     | activityName     | String | 活动名称                              | 是       |
| 活动描述     | activityDesc     | String | 活动描述                              | 是       |
| 活动图片URL  | activityImageUrl | String | 活动图片URL，多个图片用逗号分割       | 否       |
| 活动开始时间 | startTime        | String | 活动开始时间，格式：yyyy-MM-dd HH:mm:ss | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间，格式：yyyy-MM-dd HH:mm:ss | 是       |

请求示例：
```json
{
  "categoryId": 1,
  "activityName": "春季摄影大赛",
  "activityDesc": "欢迎参加春季摄影大赛",
  "activityImageUrl": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
  "startTime": "2024-03-01 09:00:00",
  "endTime": "2024-03-31 18:00:00"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*修改活动内容接口\****

##### *********.1** ***\*接口功能\****

管理后台修改活动内容信息。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 修改活动内容信息                                   |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityContent/update |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名         | 类型   | 备注                                  | 是否必填 |
| ------------ | ---------------- | ------ | ------------------------------------- | -------- |
| 主键ID       | id               | Int    | 主键ID                                | 是       |
| 活动类别ID   | categoryId       | Int    | 活动类别ID                            | 是       |
| 活动名称     | activityName     | String | 活动名称                              | 是       |
| 活动描述     | activityDesc     | String | 活动描述                              | 是       |
| 活动图片URL  | activityImageUrl | String | 活动图片URL，多个图片用逗号分割       | 否       |
| 活动开始时间 | startTime        | String | 活动开始时间，格式：yyyy-MM-dd HH:mm:ss | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间，格式：yyyy-MM-dd HH:mm:ss | 是       |

请求示例：
```json
{
  "id": 1,
  "categoryId": 1,
  "activityName": "春季摄影大赛",
  "activityDesc": "欢迎参加春季摄影大赛",
  "activityImageUrl": "http://minio.example.com/activity1.jpg",
  "startTime": "2024-03-01 09:00:00",
  "endTime": "2024-03-31 18:00:00"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*删除活动内容接口\****

##### *********.1** ***\*接口功能\****

管理后台删除活动内容，只有未发布状态的活动才能删除。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 删除活动内容，只有未发布状态的活动才能删除         |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityContent/delete |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型 | 备注   | 是否必填 |
| -------- | -------- | ---- | ------ | -------- |
| 主键ID   | id       | Int  | 主键ID | 是       |

请求示例：
```json
{
  "id": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*活动内容详情接口\****

##### *********.1** ***\*接口功能\****

管理后台查询活动内容详情信息。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 查询活动内容详情信息                               |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/activityContent/detail |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型 | 备注   | 是否必填 |
| -------- | -------- | ---- | ------ | -------- |
| 主键ID   | id       | Int  | 主键ID | 是       |

请求示例：
```json
{
  "id": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 活动内容详情                     | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动类别ID   | categoryId       | Int    | 活动类别ID                       | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动描述     | activityDesc     | String | 活动描述                         | 是       |
| 活动图片URL  | activityImageUrl | String | 活动图片URL                      | 是       |
| 活动开始时间 | startTime        | String | 活动开始时间                     | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间                     | 是       |
| 活动状态     | activityStatus   | String | 活动状态                         | 是       |
| 报名人数     | registrationCount| Int    | 报名人数                         | 是       |
| 创建时间     | createdTime      | String | 创建时间                         | 是       |
| 更新时间     | updatedTime      | String | 更新时间                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "categoryId": 1,
    "categoryName": "摄影比赛",
    "activityName": "春季摄影大赛",
    "activityDesc": "欢迎参加春季摄影大赛",
    "activityImageUrl": "http://minio.example.com/activity1.jpg",
    "startTime": "2024-03-01 09:00:00",
    "endTime": "2024-03-31 18:00:00",
    "activityStatus": "20",
    "registrationCount": 15,
    "createdTime": "2024-02-01 10:00:00",
    "updatedTime": "2024-02-01 10:00:00"
  }
}
```

##### *********.5** ***\*处理流程\****

无

## **1.3** ***\*用户报名管理模块\****

#### *********** ***\*分页查询用户报名接口\****

##### *********.1** ***\*接口功能\****

管理后台分页查询用户报名记录，支持按活动名称、用户信息和报名状态筛选。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 分页查询用户报名记录，支持按活动名称、用户信息和报名状态筛选 |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/registration/page |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名      | 类型   | 备注                                  | 是否必填 |
| ------------ | ------------- | ------ | ------------------------------------- | -------- |
| 页码         | pageNum       | Int    | 默认1                                 | 否       |
| 页大小       | pageSize      | Int    | 默认10                                | 否       |
| 活动名称     | activityName  | String | 支持模糊查询                          | 否       |
| 用户ID       | userId        | String | 用户ID                                | 否       |
| 报名状态     | status        | String | 枚举值：10(已报名)、20(已取消)        | 否       |
| 报名开始时间 | startTime     | String | 格式：yyyy-MM-dd HH:mm:ss             | 否       |
| 报名结束时间 | endTime       | String | 格式：yyyy-MM-dd HH:mm:ss             | 否       |

请求示例：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "activityName": "摄影",
  "userId": "user123",
  "status": "10",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 分页数据                         | 是       |
| 总记录数     | total            | Long   | 总记录数                         | 是       |
| 数据列表     | records          | Array  | 用户报名列表                     | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动ID       | activityId       | Int    | 活动ID                           | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 用户ID       | userId           | String | 用户ID                           | 是       |
| 用户昵称     | userNickname     | String | 用户昵称                         | 是       |
| 报名数据JSON | registrationData | String | 报名数据JSON                     | 是       |
| 报名时间     | registrationTime | String | 报名时间                         | 是       |
| 报名状态     | status           | String | 报名状态                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "activityId": 1,
        "activityName": "春季摄影大赛",
        "categoryName": "摄影比赛",
        "userId": "user123",
        "userNickname": "张三",
        "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}",
        "registrationTime": "2024-02-15 14:30:00",
        "status": "10"
      }
    ]
  }
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*用户报名详情接口\****

##### *********.1** ***\*接口功能\****

管理后台查询用户报名详情信息。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 查询用户报名详情信息                               |
| 接口提供者       | mini-app-admin                                     |
| 接口调用者       | 管理后台前端                                       |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-admin/registration/detail |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型 | 备注   | 是否必填 |
| -------- | -------- | ---- | ------ | -------- |
| 主键ID   | id       | Int  | 主键ID | 是       |

请求示例：
```json
{
  "id": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 用户报名详情                     | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动ID       | activityId       | Int    | 活动ID                           | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 用户ID       | userId           | String | 用户ID                           | 是       |
| 用户昵称     | userNickname     | String | 用户昵称                         | 是       |
| 报名数据JSON | registrationData | String | 报名数据JSON                     | 是       |
| 报名时间     | registrationTime | String | 报名时间                         | 是       |
| 报名状态     | status           | String | 报名状态                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "activityId": 1,
    "activityName": "春季摄影大赛",
    "categoryName": "摄影比赛",
    "userId": "user123",
    "userNickname": "张三",
    "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}",
    "registrationTime": "2024-02-15 14:30:00",
    "status": "10"
  }
}
```

##### *********.5** ***\*处理流程\****

无

## **1.4** ***\*小程序端接口模块\****

#### *********** ***\*小程序活动列表接口\****

##### *********.1** ***\*接口功能\****

小程序端分页查询活动列表，只显示已发布且在有效期内的活动。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 分页查询活动列表，只显示已发布且在有效期内的活动   |
| 接口提供者       | mini-app-api                                       |
| 接口调用者       | 小程序前端                                         |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-api/activity/list      |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名      | 类型   | 备注                                  | 是否必填 |
| ------------ | ------------- | ------ | ------------------------------------- | -------- |
| 页码         | pageNum       | Int    | 默认1                                 | 否       |
| 页大小       | pageSize      | Int    | 默认10                                | 否       |
| 活动类别ID   | categoryId    | Int    | 活动类别ID                            | 否       |
| 活动名称     | activityName  | String | 支持模糊查询                          | 否       |

请求示例：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "categoryId": 1,
  "activityName": "摄影"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 分页数据                         | 是       |
| 总记录数     | total            | Long   | 总记录数                         | 是       |
| 数据列表     | records          | Array  | 活动列表                         | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动类别ID   | categoryId       | Int    | 活动类别ID                       | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动描述     | activityDesc     | String | 活动描述                         | 是       |
| 活动图片URL  | activityImageUrl | String | 活动图片URL                      | 是       |
| 活动开始时间 | startTime        | String | 活动开始时间                     | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间                     | 是       |
| 活动状态     | activityStatus   | String | 活动状态                         | 是       |
| 报名人数     | registrationCount| Int    | 报名人数                         | 是       |
| 是否已报名   | isRegistered     | Boolean| 当前用户是否已报名               | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryId": 1,
        "categoryName": "摄影比赛",
        "activityName": "春季摄影大赛",
        "activityDesc": "欢迎参加春季摄影大赛",
        "activityImageUrl": "http://minio.example.com/activity1.jpg",
        "startTime": "2024-03-01 09:00:00",
        "endTime": "2024-03-31 18:00:00",
        "activityStatus": "20",
        "registrationCount": 15,
        "isRegistered": false
      }
    ]
  }
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*小程序活动详情接口\****

##### *********.1** ***\*接口功能\****

小程序端查询活动详情信息，包含表单配置和用户报名状态。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 查询活动详情信息，包含表单配置和用户报名状态       |
| 接口提供者       | mini-app-api                                       |
| 接口调用者       | 小程序前端                                         |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-api/activity/detail    |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型 | 备注   | 是否必填 |
| -------- | -------- | ---- | ------ | -------- |
| 活动ID   | id       | Int  | 活动ID | 是       |

请求示例：
```json
{
  "id": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 活动详情                         | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动类别ID   | categoryId       | Int    | 活动类别ID                       | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动描述     | activityDesc     | String | 活动描述                         | 是       |
| 活动图片URL  | activityImageUrl | String | 活动图片URL                      | 是       |
| 活动开始时间 | startTime        | String | 活动开始时间                     | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间                     | 是       |
| 活动状态     | activityStatus   | String | 活动状态                         | 是       |
| 报名人数     | registrationCount| Int    | 报名人数                         | 是       |
| 表单配置JSON | formConfig       | String | 表单配置JSON                     | 是       |
| 是否已报名   | isRegistered     | Boolean| 当前用户是否已报名               | 是       |
| 报名数据JSON | registrationData | String | 用户报名数据JSON（已报名时返回） | 否       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "categoryId": 1,
    "categoryName": "摄影比赛",
    "activityName": "春季摄影大赛",
    "activityDesc": "欢迎参加春季摄影大赛",
    "activityImageUrl": "http://minio.example.com/activity1.jpg",
    "startTime": "2024-03-01 09:00:00",
    "endTime": "2024-03-31 18:00:00",
    "activityStatus": "20",
    "registrationCount": 15,
    "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
    "isRegistered": true,
    "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}"
  }
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*小程序用户报名接口\****

##### *********.1** ***\*接口功能\****

小程序端用户报名参加活动。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 用户报名参加活动                                   |
| 接口提供者       | mini-app-api                                       |
| 接口调用者       | 小程序前端                                         |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-api/registration/register |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名         | 类型   | 备注                                                         | 是否必填 |
| ------------ | ---------------- | ------ | ------------------------------------------------------------ | -------- |
| 活动ID       | activityId       | Int    | 活动ID                                                       | 是       |
| 报名数据JSON | registrationData | String | 报名数据JSON，根据活动类别的表单配置填写，每次提交必须传递完整的JSON数据不支持部分更新 | 是       |

请求示例：
```json
{
  "activityId": 1,
  "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg,http://minio.example.com/photo2.jpg\"}"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "报名成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*小程序取消报名接口\****

##### *********.1** ***\*接口功能\****

小程序端用户取消报名。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 用户取消报名                                       |
| 接口提供者       | mini-app-api                                       |
| 接口调用者       | 小程序前端                                         |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-api/registration/cancel |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名   | 类型 | 备注   | 是否必填 |
| -------- | ---------- | ---- | ------ | -------- |
| 活动ID   | activityId | Int  | 活动ID | 是       |

请求示例：
```json
{
  "activityId": 1
}
```

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名   | 类型   | 备注                             | 是否必填 |
| -------- | ---------- | ------ | -------------------------------- | -------- |
| 状态码   | resultCode | String | 业务响应码                       | 是       |
| 提示     | resultMsg  | String | 业务响应信息                     | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "取消报名成功"
}
```

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*小程序查询我的报名接口\****

##### *********.1** ***\*接口功能\****

小程序端分页查询当前用户的报名记录。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                    |
| ---------------- | -------------------------------------------------- |
| 功能说明         | 分页查询当前用户的报名记录                         |
| 接口提供者       | mini-app-api                                       |
| 接口调用者       | 小程序前端                                         |
| 协议             | http POST                                          |
| 请求头           | Content-Type: application/json                     |
| 请求路径         | http://{ip}:{port}/mini-app-api/registration/myList |
| 超时时长         | 5000ms                                             |
| 请求报文         | 请求体： json字符串                                |
| 响应报文         | 响应体： json字符串                                |

##### *********.3** ***\*请求参数\****

| 参数名称 | 参数命名 | 类型   | 备注                           | 是否必填 |
| -------- | -------- | ------ | ------------------------------ | -------- |
| 页码     | pageNum  | Int    | 默认1                          | 否       |
| 页大小   | pageSize | Int    | 默认10                         | 否       |
| 报名状态 | status   | String | 枚举值：10(已报名)、20(已取消) | 否       |

请求示例：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "status": "10"
}
```

##### *********.4** ***\*应答参数\****

| 参数名称     | 参数命名         | 类型   | 备注                             | 是否必填 |
| ------------ | ---------------- | ------ | -------------------------------- | -------- |
| 状态码       | resultCode       | String | 业务响应码                       | 是       |
| 提示         | resultMsg        | String | 业务响应信息                     | 是       |
| 数据         | data             | Object | 分页数据                         | 是       |
| 总记录数     | total            | Long   | 总记录数                         | 是       |
| 数据列表     | records          | Array  | 我的报名列表                     | 是       |
| 主键ID       | id               | Int    | 主键ID                           | 是       |
| 活动ID       | activityId       | Int    | 活动ID                           | 是       |
| 活动名称     | activityName     | String | 活动名称                         | 是       |
| 活动类别名称 | categoryName     | String | 活动类别名称                     | 是       |
| 活动开始时间 | startTime        | String | 活动开始时间                     | 是       |
| 活动结束时间 | endTime          | String | 活动结束时间                     | 是       |
| 活动状态     | activityStatus   | String | 活动状态                         | 是       |
| 报名数据JSON | registrationData | String | 报名数据JSON                     | 是       |
| 报名时间     | registrationTime | String | 报名时间                         | 是       |
| 报名状态     | status           | String | 报名状态                         | 是       |

响应示例：
```json
{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "activityId": 1,
        "activityName": "春季摄影大赛",
        "categoryName": "摄影比赛",
        "startTime": "2024-03-01 09:00:00",
        "endTime": "2024-03-31 18:00:00",
        "activityStatus": "20",
        "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}",
        "registrationTime": "2024-02-15 14:30:00",
        "status": "10"
      }
    ]
  }
}
```

##### *********.5** ***\*处理流程\****

无
