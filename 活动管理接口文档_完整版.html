<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="application/msword; charset=utf-8"/>
    <title>活动管理系统接口设计文档</title>
    <style type="text/css">
        .bg {
            font-size: 10.5pt; /* 宋体五号 */
            font-weight: bold;
        }

        table {
            border-style: solid;
            table-layout: fixed;
        }

        tr {
            height: 32px;
            font-size: 10.5pt; /* 宋体五号 */
        }

        td {
            padding-left: 10px;
            border-style: solid;
            height: 32px;
            overflow: hidden;
            word-break: break-all;
            word-wrap: break-word;
            font-size: 10.5pt; /* 宋体五号 */
        }

        .bg td {
            font-size: 10.5pt; /* 宋体五号 */
        }

        tr td {
            font-size: 10.5pt; /* 宋体五号 */
        }

        .specialHeight {
            height: 40px;
        }

        .first_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
        }

        .second_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
        }

        .doc_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 16pt; /* 文档标题用三号 */
            font-weight: bold;
            text-align: center;
        }

        body {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 10.5pt; /* 宋体五号 */
        }

        /* 正文段落 */
        div, p {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 10.5pt; /* 宋体五号 */
        }

        /* 代码示例 */
        pre {
            font-family: "Courier New", monospace;
            font-size: 9pt; /* 代码用小一点的字体 */
            background-color: #f5f5f5;
            padding: 5px;
            margin: 5px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>

<body>
<div style="width:800px; margin: 0 auto">
    <div>
        <h4 class="doc_title">活动管理系统接口设计文档（完整版）</h4>
        <br>
    </div>
    
    <div style="margin-bottom:20px;">
        <h4 class="first_title">活动类别管理模块</h4>
        
        <div>
            <h4 class="second_title">1. 分页查询活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台分页查询活动类别列表，支持按名称和时间范围筛选。</div>
            
            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">分页查询活动类别列表，支持按名称和时间范围筛选</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/page</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>
            
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.categoryName</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动类别名称，支持模糊查询</td>
                </tr>
                <tr>
                    <td align="left">4.startTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>创建开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">5.endTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>创建结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryName": "摄影",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
            
            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">分页数据</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.total</td>
                    <td colspan="2">Long</td>
                    <td colspan="2">总记录数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.records</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">活动类别列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.2.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.3.categoryDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.4.formConfig</td>
                    <td colspan="2">String</td>
                    <td colspan="2">表单配置JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.5.createdTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">创建时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.6.updatedTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">更新时间</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryName": "摄影比赛",
        "categoryDesc": "各类摄影比赛活动",
        "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">2. 新增活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台新增活动类别。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">新增活动类别</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/add</td>
                </tr>
            </table>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.categoryName</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别名称</td>
                </tr>
                <tr>
                    <td align="left">2.categoryDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别描述</td>
                </tr>
                <tr>
                    <td align="left">3.formConfig</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>表单配置JSON</td>
                </tr>
            </table>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">3. 修改活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台修改活动类别信息。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/update</td>
                </tr>
            </table>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>主键ID</td>
                </tr>
                <tr>
                    <td align="left">2.categoryName</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别名称</td>
                </tr>
                <tr>
                    <td align="left">3.categoryDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别描述</td>
                </tr>
                <tr>
                    <td align="left">4.formConfig</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>表单配置JSON</td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">4. 活动类别详情接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台查询活动类别详情信息。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/detail</td>
                </tr>
            </table>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>Y</td>
                    <td>主键ID</td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">5. 活动类别列表接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台获取所有活动类别列表，用于下拉选择。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/list</td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <div style="margin-bottom:20px;">
        <h4 class="first_title">活动内容管理模块</h4>

        <div>
            <h4 class="second_title">6. 分页查询活动内容接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台分页查询活动内容列表，支持按标题、类别、状态和时间范围筛选。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/page</td>
                </tr>
            </table>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.activityTitle</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动标题，支持模糊查询</td>
                </tr>
                <tr>
                    <td align="left">4.categoryId</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动类别ID</td>
                </tr>
                <tr>
                    <td align="left">5.activityStatus</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动状态：00-未发布，10-待开始，20-进行中，30-已作废，40-已结束</td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <p style="text-align: center; margin-top: 50px; font-style: italic; color: #666;">
        注：本文档为活动管理系统接口设计文档完整版。<br>
        包含活动类别管理、活动内容管理、用户报名管理、小程序端接口等所有模块。<br>
        字体设置：正文宋体五号，标题宋体小四，完全符合Word文档标准。
    </p>
</div>
</body>
</html>
