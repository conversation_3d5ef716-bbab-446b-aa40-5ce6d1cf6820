# 活动管理系统产品设计文档

## 1. 概述

活动管理系统是一个支持管理员配置活动类别和活动内容，用户报名参与活动的完整系统。系统分为管理后台端和小程序端两个部分。

## 2. 数据库设计

### 2.1 活动类别表 (gt_activity_category)

```sql
CREATE TABLE `gt_activity_category` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_name` varchar(100) NOT NULL COMMENT '活动类别名称',
  `category_desc` varchar(500) DEFAULT NULL COMMENT '活动类别描述',
  `form_config` text NOT NULL COMMENT '表单配置JSON，包含字段中文名、是否必填、输入框类型、输入长度等',
  `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_name_created_time` (`category_name`, `created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动类别表';
```

**form_config字段说明：**
存储JSON格式的表单配置，支持多个文件/图片时使用逗号分割的URL字符串。

**JSON结构体定义：**
```json
[
  {
    "fieldName": "姓名",        // 字段中文名称，用于前端显示
    "required": true,           // 是否必填：true/false
    "inputType": "input",       // 输入框类型，见下方枚举值
    "maxLength": 50             // 最大输入长度
  },
  {
    "fieldName": "电话",
    "required": true,
    "inputType": "input",
    "maxLength": 11
  },
  {
    "fieldName": "作品图片",
    "required": false,
    "inputType": "file",
    "maxLength": 500
  }
]
```

**inputType枚举值说明：**
- `input`: 文本输入框
- `file`: 文件/图片上传（支持多个，逗号分割URL字符串）
- `datetime`: 时间组件
- `date`: 日期组件
- `textarea`: 多行文本框

**重要说明：**
- form_config字段每次修改都必须传递完整的JSON配置，不支持部分更新
- 有子活动内容的活动类别不支持修改表单配置
- 文件/图片上传后的URL为MinIO存储的URL地址

### 2.2 活动内容表 (gt_activity_content)

```sql
CREATE TABLE `gt_activity_content` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` int(10) NOT NULL COMMENT '活动类别ID',
  `activity_name` varchar(200) NOT NULL COMMENT '活动名称',
  `activity_desc` text COMMENT '活动介绍',
  `activity_image_url` varchar(1000) DEFAULT NULL COMMENT '活动图片URL，多个图片用逗号分割',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `status` char(2) NOT NULL DEFAULT '00' COMMENT '活动状态：00-未发布，10-待开始，20-进行中，30-已作废，40-已结束',
  `max_participants` int(10) DEFAULT NULL COMMENT '最大参与人数限制',
  `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_status_start_time` (`category_id`, `status`, `start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动内容表';
```

### 2.3 用户报名数据表 (gt_activity_registration)

```sql
CREATE TABLE `gt_activity_registration` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `activity_id` int(10) NOT NULL COMMENT '活动ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `registration_data` text NOT NULL COMMENT '报名数据JSON，存储用户填写的表单数据',
  `registration_time` datetime DEFAULT NULL COMMENT '报名时间',
  `status` char(2) DEFAULT '10' COMMENT '报名状态：10-已报名，20-已取消',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_activity_user` (`activity_id`, `user_id`),
  KEY `idx_activity_user_status` (`activity_id`, `user_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户报名数据表';
```

**registration_data字段说明：**
存储JSON格式的用户报名数据，文件/图片字段使用逗号分割的URL字符串，示例：
```json
{
  "name": "张三",
  "phone": "13800138000",
  "workImages": "http://example.com/work1.jpg,http://example.com/work2.jpg,http://example.com/work3.jpg"
}
```

### 2.4 索引设计说明

**活动内容表索引设计：**
- `idx_category_status_start_time` (`category_id`, `status`, `start_time`)
  - **用途：** 支持按类别和状态查询活动，并按开始时间排序
  - **覆盖场景：** 分页查询中的多条件筛选和排序

**用户报名表索引设计：**
- `uk_activity_user` (`activity_id`, `user_id`) - 唯一索引
  - **用途：** 保证同一用户不能重复报名同一活动
- `idx_activity_user_status` (`activity_id`, `user_id`, `status`)
  - **用途：** 支持按活动查询报名用户，并按状态筛选

**索引设计原则：**
- 使用组合索引覆盖常用查询场景，避免创建过多单列索引
- 索引字段顺序按查询频率和选择性排列
- 避免过度索引影响写入性能

## 3. 接口设计

### 3.1 管理后台端接口

#### 3.1.1 活动类别管理接口

**Controller路径：** `cn.com.mtrsz.live.controller.ActivityCategoryController`

##### 分页查询活动类别
- **接口路径：** `POST /activityCategory/page`
- **请求参数：** `ActivityCategoryPageReqVO extends QueryRequest`
```java
@ApiModelProperty(value = "活动类别名称，支持模糊查询")
private String categoryName;

@ApiModelProperty(value = "创建开始时间，格式：yyyy-MM-dd HH:mm:ss")
private Date startTime;

@ApiModelProperty(value = "创建结束时间，格式：yyyy-MM-dd HH:mm:ss")
private Date endTime;
```
- **响应结果：** `Response<PageResult<ActivityCategoryPageResVO>>`
```java
@ApiModelProperty(value = "总记录数")
private Long total;

@ApiModelProperty(value = "数据列表")
private List<ActivityCategoryPageResVO> list;

// ActivityCategoryPageResVO字段：
@ApiModelProperty(value = "主键ID")
private Integer id;

@ApiModelProperty(value = "活动类别名称")
private String categoryName;

@ApiModelProperty(value = "活动类别描述")
private String categoryDesc;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "创建时间")
private Date createdTime;
```

##### 新增活动类别
- **接口路径：** `POST /activityCategory/add`
- **请求参数：** `AddActivityCategoryReqVO`
```java
@ApiModelProperty(value = "活动类别名称", required = true)
private String categoryName;

@ApiModelProperty(value = "活动类别描述")
private String categoryDesc;

@ApiModelProperty(value = "表单配置JSON，每次修改必须传递完整的JSON配置不支持部分更新。inputType枚举值：input(文本输入框)、file(文件/图片上传支持多个逗号分割MinIO的URL)、datetime(时间组件)、date(日期组件)、textarea(多行文本框)", required = true)
private String formConfig;
```
- **响应结果：** `Response<AddActivityCategoryResVO extends BasicResponseVO>`

##### 修改活动类别
- **接口路径：** `POST /activityCategory/update`
- **请求参数：** `UpdateActivityCategoryReqVO`
```java
@ApiModelProperty(value = "主键ID", required = true)
private Integer id;

@ApiModelProperty(value = "活动类别名称")
private String categoryName;

@ApiModelProperty(value = "活动类别描述")
private String categoryDesc;

@ApiModelProperty(value = "表单配置JSON，每次修改必须传递完整的JSON配置不支持部分更新。inputType枚举值：input(文本输入框)、file(文件/图片上传支持多个逗号分割MinIO的URL)、datetime(时间组件)、date(日期组件)、textarea(多行文本框)")
private String formConfig;
```
- **响应结果：** `Response<UpdateActivityCategoryResVO extends BasicResponseVO>`

##### 删除活动类别
- **接口路径：** `POST /activityCategory/delete`
- **请求参数：** `DeleteActivityCategoryReqVO`
```java
@ApiModelProperty(value = "主键ID", required = true)
private Integer id;
```
- **响应结果：** `Response<DeleteActivityCategoryResVO extends BasicResponseVO>`

##### 活动类别详情查询
- **接口路径：** `GET /activityCategory/detail`
- **请求参数：** `id` - Integer - 主键ID（必填）
- **响应结果：** `Response<ActivityCategoryDetailResVO>`
```java
@ApiModelProperty(value = "主键ID")
private Integer id;

@ApiModelProperty(value = "活动类别名称")
private String categoryName;

@ApiModelProperty(value = "活动类别描述")
private String categoryDesc;

@ApiModelProperty(value = "表单配置JSON")
private String formConfig;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "创建时间")
private Date createdTime;

@ApiModelProperty(value = "更新人")
private String updatedBy;

@ApiModelProperty(value = "更新时间")
private Date updatedTime;
```

#### 3.1.2 活动内容管理接口

**Controller路径：** `cn.com.mtrsz.live.controller.ActivityContentController`

##### 分页查询活动内容
- **接口路径：** `POST /activityContent/page`
- **请求参数：** `ActivityContentPageReqVO extends QueryRequest`
```java
@ApiModelProperty(value = "活动类别ID")
private Integer categoryId;

@ApiModelProperty(value = "活动名称，支持模糊查询")
private String activityName;

@ApiModelProperty(value = "活动状态，枚举值：00(未发布)、10(待开始)、20(进行中)、30(已作废)、40(已结束)")
private String status;

@ApiModelProperty(value = "活动开始时间范围-开始，格式：yyyy-MM-dd HH:mm:ss")
private Date startTimeBegin;

@ApiModelProperty(value = "活动开始时间范围-结束，格式：yyyy-MM-dd HH:mm:ss")
private Date startTimeEnd;

@ApiModelProperty(value = "活动结束时间范围-开始，格式：yyyy-MM-dd HH:mm:ss")
private Date endTimeBegin;

@ApiModelProperty(value = "活动结束时间范围-结束，格式：yyyy-MM-dd HH:mm:ss")
private Date endTimeEnd;
```
- **响应结果：** `Response<PageResult<ActivityContentPageResVO>>`
```java
@ApiModelProperty(value = "总记录数")
private Long total;

@ApiModelProperty(value = "数据列表")
private List<ActivityContentPageResVO> list;

// ActivityContentPageResVO字段：
@ApiModelProperty(value = "主键ID")
private Integer id;

@ApiModelProperty(value = "活动类别ID")
private Integer categoryId;

@ApiModelProperty(value = "活动类别名称")
private String categoryName;

@ApiModelProperty(value = "活动名称")
private String activityName;

@ApiModelProperty(value = "活动图片URL，多个图片逗号分割")
private String activityImageUrl;

@ApiModelProperty(value = "活动开始时间")
private Date startTime;

@ApiModelProperty(value = "活动结束时间")
private Date endTime;

@ApiModelProperty(value = "活动状态")
private String status;

@ApiModelProperty(value = "已报名人数，实时计算")
private Integer registeredCount;

@ApiModelProperty(value = "最大参与人数限制")
private Integer maxParticipants;

@ApiModelProperty(value = "创建时间")
private Date createdTime;
```

##### 新增活动内容
- **接口路径：** `POST /activityContent/add`
- **请求参数：** `AddActivityContentReqVO`
```java
@ApiModelProperty(value = "活动类别ID", required = true)
private Integer categoryId;

@ApiModelProperty(value = "活动名称", required = true)
private String activityName;

@ApiModelProperty(value = "活动介绍")
private String activityDesc;

@ApiModelProperty(value = "活动图片URL，多个图片用逗号分割")
private String activityImageUrl;

@ApiModelProperty(value = "活动开始时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
private Date startTime;

@ApiModelProperty(value = "活动结束时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
private Date endTime;

@ApiModelProperty(value = "最大参与人数限制")
private Integer maxParticipants;
```
- **响应结果：** `Response<AddActivityContentResVO extends BasicResponseVO>`

##### 修改活动内容
- **接口路径：** `POST /activityContent/update`
- **请求参数：** `UpdateActivityContentReqVO`
  - `id`: Integer - 主键ID（必填）
  - `categoryId`: Integer - 活动类别ID
  - `activityName`: String - 活动名称
  - `activityDesc`: String - 活动介绍
  - `activityImageUrl`: String - 活动图片URL
  - `startTime`: Date - 活动开始时间
  - `endTime`: Date - 活动结束时间
  - `maxParticipants`: Integer - 最大参与人数限制
- **响应结果：** `Response<UpdateActivityContentResVO>`
  - `id`: Integer - 主键ID
  - `activityName`: String - 活动名称

##### 删除活动内容
- **接口路径：** `POST /activityContent/delete`
- **请求参数：** `DeleteActivityContentReqVO`
  - `id`: Integer - 主键ID（必填）
- **响应结果：** `Response<DeleteActivityContentResVO>`
  - `success`: Boolean - 删除是否成功

##### 活动状态修改
- **接口路径：** `POST /activityContent/updateStatus`
- **请求参数：** `UpdateActivityStatusReqVO`
  - `id`: Integer - 主键ID（必填）
  - `status`: String - 活动状态（必填）
- **响应结果：** `Response<UpdateActivityStatusResVO>`
  - `id`: Integer - 主键ID
  - `status`: String - 更新后的状态

##### 活动内容详情查询
- **接口路径：** `GET /activityContent/detail`
- **请求参数：** `id` - Integer - 主键ID（必填）
- **响应结果：** `Response<ActivityContentDetailResVO>`
  - `id`: Integer - 主键ID
  - `categoryId`: Integer - 活动类别ID
  - `categoryName`: String - 活动类别名称
  - `activityName`: String - 活动名称
  - `activityDesc`: String - 活动介绍
  - `activityImageUrl`: String - 活动图片URL
  - `startTime`: Date - 活动开始时间
  - `endTime`: Date - 活动结束时间
  - `status`: String - 活动状态
  - `registeredCount`: Integer - 已报名人数
  - `maxParticipants`: Integer - 最大参与人数限制
  - `createdBy`: String - 创建人
  - `createdTime`: Date - 创建时间
  - `updatedBy`: String - 更新人
  - `updatedTime`: Date - 更新时间

##### 分页查询用户报名数据
- **接口路径：** `GET /activityContent/registrations`
- **请求参数：** `ActivityRegistrationPageReqVO`
  - `pageNum`: Integer - 页码（默认1）
  - `pageSize`: Integer - 页大小（默认10）
  - `activityId`: Integer - 活动ID（必填）
  - `userId`: String - 用户ID
  - `registrationTimeBegin`: Date - 报名时间范围-开始
  - `registrationTimeEnd`: Date - 报名时间范围-结束
- **响应结果：** `Response<PageResult<ActivityRegistrationPageResVO>>`
  - `total`: Long - 总记录数
  - `list`: List<ActivityRegistrationPageResVO> - 数据列表
    - `id`: Integer - 主键ID
    - `activityId`: Integer - 活动ID
    - `userId`: String - 用户ID
    - `registrationData`: String - 报名数据JSON
    - `registrationTime`: Date - 报名时间
    - `status`: String - 报名状态

### 3.2 小程序端接口

**Controller路径：** `cn.com.mtrsz.live.controller.ActivityMiniController`

##### 活动列表查询
- **接口路径：** `GET /activityMini/list`
- **请求参数：** `ActivityMiniListReqVO`
  - `categoryId`: Integer - 活动类别ID
  - `status`: String - 活动状态
  - `pageNum`: Integer - 页码（默认1）
  - `pageSize`: Integer - 页大小（默认10）
- **响应结果：** `Response<ActivityMiniListResVO>`
  - `activityList`: List<ActivityMiniItemResVO> - 活动列表
    - `id`: Integer - 活动ID
    - `categoryId`: Integer - 活动类别ID
    - `categoryName`: String - 活动类别名称
    - `activityName`: String - 活动名称
    - `activityDesc`: String - 活动介绍
    - `activityImageUrl`: String - 活动图片URL
    - `startTime`: Date - 活动开始时间
    - `endTime`: Date - 活动结束时间
    - `status`: String - 活动状态
    - `registeredCount`: Integer - 已报名人数
    - `maxParticipants`: Integer - 最大参与人数限制
  - `categoryFormConfig`: List<CategoryFormConfigResVO> - 活动类别表单配置
    - `categoryId`: Integer - 活动类别ID
    - `formConfig`: String - 表单配置JSON

##### 提交报名信息
- **接口路径：** `POST /activityMini/register`
- **请求参数：** `ActivityRegistrationReqVO`
```java
@ApiModelProperty(value = "活动ID", required = true)
private Integer activityId;

@ApiModelProperty(value = "用户ID", required = true)
private String userId;

@ApiModelProperty(value = "报名数据JSON，根据活动类别的表单配置填写，文件/图片字段使用逗号分割的URL字符串", required = true)
private String registrationData;
```
- **响应结果：** `Response<ActivityRegistrationResVO>`
```java
@ApiModelProperty(value = "报名记录ID")
private Integer id;

@ApiModelProperty(value = "活动ID")
private Integer activityId;

@ApiModelProperty(value = "报名时间")
private Date registrationTime;
```

##### 个人历史报名数据列表
- **接口路径：** `POST /activityMini/myRegistrations`
- **请求参数：** `MyRegistrationListReqVO extends QueryRequest`
```java
@ApiModelProperty(value = "用户ID", required = true)
private String userId;
```
- **响应结果：** `Response<MyRegistrationListResVO>`
  - `total`: Long - 总记录数
  - `list`: List<MyRegistrationItemResVO> - 报名记录列表
    - `id`: Integer - 报名记录ID
    - `activityId`: Integer - 活动ID
    - `activityName`: String - 活动名称
    - `activityImageUrl`: String - 活动图片URL
    - `startTime`: Date - 活动开始时间
    - `endTime`: Date - 活动结束时间
    - `status`: String - 活动状态
    - `registrationTime`: Date - 报名时间
    - `registrationStatus`: String - 报名状态

##### 个人指定活动详情查询
- **接口路径：** `GET /activityMini/myRegistrationDetail`
- **请求参数：**
  - `activityId`: Integer - 活动ID（必填）
  - `userId`: String - 用户ID（必填）
- **响应结果：** `Response<MyRegistrationDetailResVO>`
  - `id`: Integer - 报名记录ID
  - `activityId`: Integer - 活动ID
  - `activityName`: String - 活动名称
  - `activityDesc`: String - 活动介绍
  - `activityImageUrl`: String - 活动图片URL
  - `startTime`: Date - 活动开始时间
  - `endTime`: Date - 活动结束时间
  - `categoryName`: String - 活动类别名称
  - `registrationData`: String - 报名数据JSON
  - `registrationTime`: Date - 报名时间
  - `registrationStatus`: String - 报名状态

## 4. 前端传参规范说明

### 4.1 通用传参规范
1. **时间格式：** 所有时间字段统一使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **文件/图片字段：** 支持多个文件/图片时，使用逗号分割的MinIO URL字符串，如：`"minio_url1.jpg,minio_url2.jpg,minio_url3.jpg"`
3. **JSON字段：** form_config和registration_data字段传递完整的JSON字符串
4. **枚举值说明：**
   - **活动状态：** `00`(未发布)、`10`(待开始)、`20`(进行中)、`30`(已作废)、`40`(已结束)
   - **报名状态：** `10`(已报名)、`20`(已取消)
   - **表单输入类型：** `input`(文本输入框)、`file`(文件/图片上传)、`datetime`(时间组件)、`date`(日期组件)、`textarea`(多行文本框)

### 4.2 特殊字段说明
1. **form_config字段：**
   - **传递方式：** 每次修改都需要传递完整的表单配置JSON，不支持部分更新
   - **修改限制：** 有子活动内容的活动类别不支持修改此字段
   - **JSON结构：** 必须包含fieldName、required、inputType、maxLength等完整字段（不需要fieldKey）
2. **registration_data字段：** 根据对应活动类别的form_config配置填写，文件/图片字段使用逗号分割MinIO URL
3. **registered_count字段：** 该字段不存储在数据库中，查询时实时计算，前端不需要传递此字段

## 5. 重要业务规则和注意事项

### 5.1 活动类别管理
1. **删除限制：** 已经有绑定子活动的活动类别不支持删除
2. **修改限制：** 有子活动内容的活动类别不支持修改表单配置（form_config字段）
3. **表单配置：** form_config字段存储JSON格式的表单配置，每次修改必须传递完整配置，不支持部分更新
4. **字段验证：** 需要验证表单配置的完整性和合法性
5. **状态管理：** 活动类别表不需要状态字段，默认为启用状态

### 5.2 活动内容管理
1. **删除限制：** 只有未发布状态（00）的数据才支持删除
2. **修改限制：** 只有未发布状态（00）的数据才支持修改
3. **状态流转：** 00-未发布 → 10-待开始 → 20-进行中 → 40-已结束，30-已作废可从任意状态转换
4. **报名人数：** registered_count字段不存储在数据库中，查询时实时计算（统计gt_activity_registration表中对应活动的有效报名记录数）
5. **时间验证：** 活动开始时间必须小于结束时间
6. **图片处理：** activity_image_url字段支持多个图片，使用逗号分割的URL字符串存储

### 5.3 用户报名管理
1. **唯一性约束：** 同一用户不能重复报名同一活动
2. **数据格式：** registration_data存储JSON格式的用户填写数据
3. **文件/图片处理：** 文件和图片类型字段使用逗号分割的URL字符串存储，支持多个文件/图片
4. **表单验证：** 根据活动类别的form_config验证用户提交的数据完整性和必填项
5. **表单配置传递：** form_config字段每次都需要全量传递，不支持部分更新

### 5.4 小程序端特殊处理
1. **数据过滤：** 查询时排除【未发布】、【已作废】状态的活动
2. **用户身份：** 需要通过用户身份标识查询个人报名记录
3. **动态表单：** 根据活动类别的配置动态生成报名表单

## 6. VO类汇总表

### 6.1 活动类别相关VO类
- **ActivityCategoryPageReqVO** - 活动类别分页查询请求
- **ActivityCategoryPageResVO** - 活动类别分页查询响应
- **AddActivityCategoryReqVO** - 新增活动类别请求
- **AddActivityCategoryResVO** - 新增活动类别响应
- **UpdateActivityCategoryReqVO** - 修改活动类别请求
- **UpdateActivityCategoryResVO** - 修改活动类别响应
- **DeleteActivityCategoryReqVO** - 删除活动类别请求
- **DeleteActivityCategoryResVO** - 删除活动类别响应
- **ActivityCategoryDetailResVO** - 活动类别详情响应

### 6.2 活动内容相关VO类
- **ActivityContentPageReqVO** - 活动内容分页查询请求
- **ActivityContentPageResVO** - 活动内容分页查询响应
- **AddActivityContentReqVO** - 新增活动内容请求
- **AddActivityContentResVO** - 新增活动内容响应
- **UpdateActivityContentReqVO** - 修改活动内容请求
- **UpdateActivityContentResVO** - 修改活动内容响应
- **DeleteActivityContentReqVO** - 删除活动内容请求
- **DeleteActivityContentResVO** - 删除活动内容响应
- **UpdateActivityStatusReqVO** - 修改活动状态请求
- **UpdateActivityStatusResVO** - 修改活动状态响应
- **ActivityContentDetailResVO** - 活动内容详情响应

### 6.3 报名数据相关VO类
- **ActivityRegistrationPageReqVO** - 报名数据分页查询请求
- **ActivityRegistrationPageResVO** - 报名数据分页查询响应
- **ActivityRegistrationReqVO** - 提交报名信息请求
- **ActivityRegistrationResVO** - 提交报名信息响应

### 6.4 小程序端相关VO类
- **ActivityMiniListReqVO** - 小程序活动列表查询请求
- **ActivityMiniListResVO** - 小程序活动列表查询响应
- **ActivityMiniItemResVO** - 小程序活动列表项响应
- **CategoryFormConfigResVO** - 活动类别表单配置响应
- **MyRegistrationListReqVO** - 个人报名列表查询请求
- **MyRegistrationListResVO** - 个人报名列表查询响应
- **MyRegistrationItemResVO** - 个人报名列表项响应
- **MyRegistrationDetailResVO** - 个人报名详情响应

### 6.5 通用VO类
- **QueryRequest** - 分页查询基础请求类，包含pageNum和pageSize，所有分页查询接口的ReqVO都继承此类
- **BasicResponseVO** - 基础响应VO，包含resultCode和resultMsg，所有新增/修改/删除接口的ResVO都继承此类
- **PageResult<T>** - 分页结果封装类
- **Response<T>** - 统一响应结果封装类

**QueryRequest定义：**
```java
public class QueryRequest {
    @ApiModelProperty(value = "页码，默认1")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小，默认10")
    private Integer pageSize;
}
```

**BasicResponseVO定义：**
```java
public class BasicResponseVO {
    @ApiModelProperty(value = "业务响应码", required = true)
    private String resultCode;

    @ApiModelProperty(value = "业务响应信息", required = true)
    private String resultMsg;
}
```

## 7. 技术实现要点

### 7.1 代码模板参考
- 严格按照 `cn.com.mtrsz.live` 包下的现有代码结构和风格
- Controller、ReqVO、ResVO的命名和注解规范
- 使用Swagger注解生成接口文档

### 7.2 分页处理
- 使用统一的分页请求和响应格式
- PageResult包含总数、当前页、页大小等信息

### 7.3 JSON字段处理
- form_config和registration_data字段的JSON序列化和反序列化
- 前端提交时的数据验证和转换

### 7.4 状态管理
- 活动状态的合法性验证
- 状态变更时的业务逻辑处理

### 7.5 数据一致性
- 报名人数的实时计算（不存储，查询时统计）
- 删除操作的关联性检查
