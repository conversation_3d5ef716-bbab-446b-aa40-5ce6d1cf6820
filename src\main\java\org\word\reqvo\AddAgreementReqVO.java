package org.word.reqvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     AddAgreementReqVO
 * Author:       SU
 * Date:         2023/7/6 17:29
 * Description:
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */

@Data
@ApiModel("增加用户协议请求参数")
public class AddAgreementReqVO {

    /**
     * 协议类型
     */
    @ApiModelProperty(value = "协议类型", required = true)
    private String type;

    /**
     * 协议名称
     */
    @ApiModelProperty(value = "协议名称", required = true)
    private String title;

    /**
     * 协议内容
     */
    @ApiModelProperty(value = "协议内容", required = true)
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
