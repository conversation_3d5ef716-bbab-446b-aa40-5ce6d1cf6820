package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     AgreementResVO
 * Author:       SU
 * Date:         2023/7/6 14:28
 * Description:
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */

@Data
@ApiModel("协议返回内容参数")
public class AgreementResVO {

    /**
     * 协议名称
     */
    @ApiModelProperty(value = "协议名称", required = true)
    private String title;

    /**
     * 协议内容
     */
    @ApiModelProperty(value = "协议内容", required = true)
    private String content;

}
