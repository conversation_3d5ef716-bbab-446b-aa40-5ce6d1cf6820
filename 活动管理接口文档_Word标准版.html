<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="application/msword; charset=utf-8"/>
    <title>活动管理系统接口设计文档</title>
    <style type="text/css">
        .bg {
            font-size: 14.5px;
            font-weight: bold;
        }

        table {
            border-style: solid;
            table-layout: fixed;
        }

        tr {
            height: 32px;
            font-size: 12px;
        }

        td {
            padding-left: 10px;
            border-style: solid;
            height: 32px;
            overflow: hidden;
            word-break: break-all;
            word-wrap: break-word;
            font-size: 14.5px;
        }

        .bg td {
            font-size: 14.5px;
        }

        tr td {
            font-size: 14.5px;
        }

        .specialHeight {
            height: 40px;
        }

        .first_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px;
        }

        .second_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px;
        }

        .doc_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px;
        }

        body {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 14px;
        }
    </style>
</head>

<body>
<div style="width:800px; margin: 0 auto">
    <div>
        <h4 class="doc_title">活动管理系统接口设计文档</h4>
        <br>
    </div>
    
    <div style="margin-bottom:20px;">
        <h4 class="first_title">活动类别管理模块</h4>
        
        <div>
            <h4 class="second_title">分页查询活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台分页查询活动类别列表，支持按名称和时间范围筛选。</div>
            
            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">分页查询活动类别列表，支持按名称和时间范围筛选</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/page</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>
            
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.categoryName</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动类别名称，支持模糊查询</td>
                </tr>
                <tr>
                    <td align="left">4.startTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>创建开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">5.endTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>创建结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryName": "摄影",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
            
            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">分页数据</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.total</td>
                    <td colspan="2">Long</td>
                    <td colspan="2">总记录数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.records</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">活动类别列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.2.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.3.categoryDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.4.formConfig</td>
                    <td colspan="2">String</td>
                    <td colspan="2">表单配置JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.5.createdTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">创建时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.6.updatedTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">更新时间</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryName": "摄影比赛",
        "categoryDesc": "各类摄影比赛活动",
        "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">新增活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台新增活动类别。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">新增活动类别</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/add</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.categoryName</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别名称</td>
                </tr>
                <tr>
                    <td align="left">2.categoryDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别描述</td>
                </tr>
                <tr>
                    <td align="left">3.formConfig</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50},{\"fieldName\":\"作品图片\",\"required\":true,\"inputType\":\"upload\",\"maxCount\":3}]"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <p style="text-align: center; margin-top: 50px; font-style: italic; color: #666;">
        注：本文档为Word复制标准版本，完全按照word-clc.html的格式制作。<br>
        如需完整版本，请参考原始HTML文档。
    </p>
</div>
</body>
</html>
