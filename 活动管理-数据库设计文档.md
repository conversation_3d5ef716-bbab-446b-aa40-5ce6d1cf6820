### **1.1.1** ***\*gt_activity_category [\****活动类别表***\*]\****

| **#** | **字段**       | **名称**     | **数据类型**  | **主键** | **非空** | **默认值** | **备注说明**                                                 |
| ----- | -------------- | ------------ | ------------- | -------- | -------- | ---------- | ------------------------------------------------------------ |
| 1     | id             | 主键ID       | INT(10)       | √        | √        |            |                                                              |
| 2     | category_name  | 活动类别名称 | VARCHAR(100)  |          | √        |            | 唯一约束                                                     |
| 3     | form_config    | 表单配置JSON | TEXT          |          | √        |            | 存储动态表单字段配置，JSON格式                               |
| 4     | created_by     | 创建人       | VARCHAR(32)   |          |          |            |                                                              |
| 5     | created_time   | 创建时间     | DATETIME      |          |          |            |                                                              |
| 6     | updated_by     | 更新人       | VARCHAR(32)   |          |          |            |                                                              |
| 7     | updated_time   | 更新时间     | DATETIME      |          |          |            |                                                              |

### **1.1.2** ***\*gt_activity_content [\****活动内容表***\*]\****

| **#** | **字段**           | **名称**     | **数据类型**  | **主键** | **非空** | **默认值** | **备注说明**                                                 |
| ----- | ------------------ | ------------ | ------------- | -------- | -------- | ---------- | ------------------------------------------------------------ |
| 1     | id                 | 主键ID       | INT(10)       | √        | √        |            |                                                              |
| 2     | category_id        | 活动类别ID   | INT(10)       |          | √        |            | 关联gt_activity_category.id                                  |
| 3     | activity_name      | 活动名称     | VARCHAR(200)  |          | √        |            |                                                              |
| 4     | activity_desc      | 活动描述     | TEXT          |          |          |            | 支持富文本                                                   |
| 5     | activity_image_url | 活动图片URL  | VARCHAR(1000) |          |          |            | 多个图片用逗号分割，MinIO存储URL                             |
| 6     | start_time         | 活动开始时间 | DATETIME      |          | √        |            |                                                              |
| 7     | end_time           | 活动结束时间 | DATETIME      |          | √        |            |                                                              |
| 8     | status             | 活动状态     | CHAR(2)       |          | √        | 00         | 00-未发布，10-待开始，20-进行中，30-已作废，40-已结束        |
| 9     | created_by         | 创建人       | VARCHAR(32)   |          |          |            |                                                              |
| 10    | created_time       | 创建时间     | DATETIME      |          |          |            |                                                              |
| 11    | updated_by         | 更新人       | VARCHAR(32)   |          |          |            |                                                              |
| 12    | updated_time       | 更新时间     | DATETIME      |          |          |            |                                                              |

### **1.1.3** ***\*gt_activity_registration [\****用户报名表***\*]\****

| **#** | **字段**          | **名称**     | **数据类型** | **主键** | **非空** | **默认值** | **备注说明**                                                 |
| ----- | ----------------- | ------------ | ------------ | -------- | -------- | ---------- | ------------------------------------------------------------ |
| 1     | id                | 主键ID       | INT(10)      | √        | √        |            |                                                              |
| 2     | activity_id       | 活动ID       | INT(10)      |          | √        |            | 关联gt_activity_content.id                                   |
| 3     | user_id           | 用户ID       | VARCHAR(50)  |          | √        |            |                                                              |
| 4     | registration_data | 报名数据JSON | TEXT         |          | √        |            | 存储用户填写的表单数据，JSON格式                             |
| 5     | registration_time | 报名时间     | DATETIME     |          |          |            |                                                              |
| 6     | status            | 报名状态     | CHAR(2)      |          |          | 10         | 10-已报名，20-已取消                                         |
