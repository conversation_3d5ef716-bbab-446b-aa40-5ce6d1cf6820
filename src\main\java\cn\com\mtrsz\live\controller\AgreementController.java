package cn.com.mtrsz.live.controller;


import cn.com.mtrsz.live.reqvo.AddAgreementReqVO;
import cn.com.mtrsz.live.reqvo.UpdateAgreementReqVO;
import cn.com.mtrsz.live.resvo.AgreementResVO;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.xml.ws.Response;


/**
 * Copyright (C), 2010-2023
 * FileName:     AgreementController
 * Author:       SU
 * Date:         2023/7/6 11:31
 * Description:  协议接口
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@RestController
@RequestMapping("/agr")
@Api(tags = "协议接口")
public class AgreementController {

    @ApiOperation(value = "根据类型查询协议信息")
    @GetMapping(value = "/getAgreement")
    public Response<AgreementResVO> getAgreement(@ApiParam(value = "协议类型", required = true) @RequestParam("type") String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        return null;
    }

    @ApiOperation(value = "增加协议信息")
    @PostMapping("/addAgreement")
    public Response<AgreementResVO> addAgreement(@RequestBody AddAgreementReqVO reqVO) {
        if (ObjectUtil.isEmpty(reqVO)) {
            return null;
        }
        return null;
    }

    @ApiOperation(value = "修改协议信息")
    @PostMapping(value = "/updateAgreement")
    public Response<AgreementResVO> updateAgreement(@RequestBody UpdateAgreementReqVO reqVO) {
        if (ObjectUtil.isEmpty(reqVO)) {
            return null;
        }
        return null;
    }

    @ApiOperation(value = "删除协议信息")
    @PostMapping(value = "/deleteAgreement")
    public Response<AgreementResVO> deleteAgreement(@RequestBody UpdateAgreementReqVO reqVO) {
        if (ObjectUtil.isEmpty(reqVO)) {
            return null;
        }
        return null;
    }

}
