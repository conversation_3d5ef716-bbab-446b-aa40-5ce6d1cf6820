package org.word.reqvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     UpdateAgreementReqVO
 * Author:       SU
 * Date:         2023/7/7 10:32
 * Description:
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("修改用户协议请求参数")
public class UpdateAgreementReqVO {

    /**
     * 协议id
     */
    @ApiModelProperty(value = "协议id")
    private Integer id;

    /**
     * 协议类型
     */
    @ApiModelProperty(value = "协议类型")
    private String type;

    /**
     * 协议名称
     */
    @ApiModelProperty(value = "协议名称")
    private String title;

    /**
     * 协议内容
     */
    @ApiModelProperty(value = "协议内容")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
