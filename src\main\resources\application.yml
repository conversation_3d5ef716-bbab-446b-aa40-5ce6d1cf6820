server:
  port: 8099
  tomcat:
    max-threads: 800
    uri-encoding: UTF-8

spring:
  application:
    name: swagger2word
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    cache: false
    servlet:
      content-type: text/html
    enabled: true
    encoding: UTF-8
    mode: HTML5
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
# Swagger json url address
# etc. https://petstore.swagger.io/
#swagger.url: https://petstore.swagger.io/v2/swagger.json
swagger.url: http://http://localhost:8084/mini-payment/v2/api-docs
#swagger.url: http://localhost:8080/risk-audit/v2/api-docs







