<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动管理系统接口设计文档</title>
    <style>
        body {
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt; /* 五号字体 */
            line-height: 1.5;
            margin: 40px;
            color: #000;
        }
        
        h1 {
            font-family: "宋体", SimSun, serif;
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        h2 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt; /* 小四字体 */
            font-weight: bold;
            margin: 15px 0 10px 0;
        }
        
        h3 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt; /* 小四字体 */
            font-weight: bold;
            margin: 15px 0 10px 0;
        }
        
        h4 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt; /* 小四字体 */
            font-weight: bold;
            margin: 15px 0 10px 0;
        }
        
        h5 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt; /* 小四字体 */
            font-weight: bold;
            margin: 10px 0 8px 0;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt;
        }
        
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 10.5pt !important;
            text-align: center;
        }
        
        pre {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: "Courier New", monospace;
            font-size: 9pt;
            overflow-x: auto;
            margin: 10px 0;
            white-space: pre-wrap;
            line-height: 1.2; /* 设置较小的行高 */
        }
        
        code {
            font-family: "Courier New", monospace;
            font-size: 9pt;
            background-color: #f5f5f5;
            line-height: 1.2; /* 设置较小的行高 */
            padding: 2px 4px;
        }
        
        p {
            margin: 8px 0;
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt !important; /* 强制五号字体 */
        }
        
        .interface-section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }

        /* 专门为Word复制优化的样式 */
        * {
            font-family: "宋体", SimSun, serif !important;
        }

        body *, p *, td *, th *, div * {
            font-size: 10.5pt !important; /* 强制所有正文为五号字体 */
        }

        h1, h1 * {
            font-size: 16pt !important;
        }

        h2, h2 * {
            font-size: 12pt !important; /* 小四字体 */
        }

        h3, h3 *, h4, h4 *, h5, h5 * {
            font-size: 12pt !important; /* 小四字体 */
        }
    </style>
</head>
<body>
    <h1>活动管理系统接口设计文档</h1>

    <h2>活动类别管理模块</h2>

    <div class="interface-section">
        <h3>分页查询活动类别接口</h3>
        
        <h4>接口功能</h4>
        <p>管理后台分页查询活动类别列表，支持按名称和时间范围筛选。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>分页查询活动类别列表，支持按名称和时间范围筛选</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/page</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>页码</td><td>pageNum</td><td>Int</td><td>默认1</td><td>否</td></tr>
            <tr><td>页大小</td><td>pageSize</td><td>Int</td><td>默认10</td><td>否</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>支持模糊查询</td><td>否</td></tr>
            <tr><td>创建开始时间</td><td>startTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
            <tr><td>创建结束时间</td><td>endTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryName": "摄影",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>分页数据</td><td>是</td></tr>
            <tr><td>总记录数</td><td>total</td><td>Long</td><td>总记录数</td><td>是</td></tr>
            <tr><td>数据列表</td><td>records</td><td>Array</td><td>活动类别列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON</td><td>是</td></tr>
            <tr><td>创建时间</td><td>createdTime</td><td>String</td><td>创建时间</td><td>是</td></tr>
            <tr><td>更新时间</td><td>updatedTime</td><td>String</td><td>更新时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryName": "摄影比赛",
        "categoryDesc": "各类摄影比赛活动",
        "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>新增活动类别接口</h3>
        
        <h4>接口功能</h4>
        <p>管理后台新增活动类别。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>新增活动类别</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/add</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50},{\"fieldName\":\"作品图片\",\"required\":true,\"inputType\":\"upload\",\"maxCount\":3}]"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>修改活动类别接口</h3>

        <h4>接口功能</h4>
        <p>管理后台修改活动类别信息。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>修改活动类别信息</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/update</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1,
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>删除活动类别接口</h3>

        <h4>接口功能</h4>
        <p>管理后台删除活动类别，只有未被活动内容使用的类别才能删除。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>删除活动类别，只有未被活动内容使用的类别才能删除</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/delete</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>活动类别详情接口</h3>

        <h4>接口功能</h4>
        <p>管理后台查询活动类别详情信息。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>查询活动类别详情信息</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/detail</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>活动类别详情</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON</td><td>是</td></tr>
            <tr><td>创建时间</td><td>createdTime</td><td>String</td><td>创建时间</td><td>是</td></tr>
            <tr><td>更新时间</td><td>updatedTime</td><td>String</td><td>更新时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "categoryName": "摄影比赛",
    "categoryDesc": "各类摄影比赛活动",
    "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
    "createdTime": "2024-01-01 10:00:00",
    "updatedTime": "2024-01-01 10:00:00"
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>活动类别列表接口</h3>

        <h4>接口功能</h4>
        <p>管理后台获取所有活动类别列表，用于下拉选择。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>获取所有活动类别列表，用于下拉选择</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/list</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <p>无请求参数</p>

        <p>请求示例：</p>
        <pre><code>{}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Array</td><td>活动类别列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryName": "摄影比赛"
    },
    {
      "id": 2,
      "categoryName": "文艺演出"
    }
  ]
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <h2>活动内容管理模块</h2>

    <div class="interface-section">
        <h3>分页查询活动内容接口</h3>

        <h4>接口功能</h4>
        <p>管理后台分页查询活动内容列表，支持按标题、类别、状态和时间范围筛选。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>分页查询活动内容列表，支持按标题、类别、状态和时间范围筛选</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityContent/page</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>页码</td><td>pageNum</td><td>Int</td><td>默认1</td><td>否</td></tr>
            <tr><td>页大小</td><td>pageSize</td><td>Int</td><td>默认10</td><td>否</td></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>支持模糊查询</td><td>否</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>否</td></tr>
            <tr><td>活动状态</td><td>status</td><td>String</td><td>未发布、待开始、进行中、已作废、已结束</td><td>否</td></tr>
            <tr><td>创建开始时间</td><td>startTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
            <tr><td>创建结束时间</td><td>endTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "pageNum": 1,
  "pageSize": 10,
  "title": "摄影",
  "categoryId": 1,
  "status": "进行中",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>分页数据</td><td>是</td></tr>
            <tr><td>总记录数</td><td>total</td><td>Long</td><td>总记录数</td><td>是</td></tr>
            <tr><td>数据列表</td><td>records</td><td>Array</td><td>活动内容列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动描述</td><td>description</td><td>String</td><td>活动描述</td><td>是</td></tr>
            <tr><td>活动图片</td><td>images</td><td>String</td><td>活动图片URL，多个用逗号分隔</td><td>是</td></tr>
            <tr><td>活动地点</td><td>location</td><td>String</td><td>活动地点</td><td>是</td></tr>
            <tr><td>开始时间</td><td>startTime</td><td>String</td><td>活动开始时间</td><td>是</td></tr>
            <tr><td>结束时间</td><td>endTime</td><td>String</td><td>活动结束时间</td><td>是</td></tr>
            <tr><td>报名截止时间</td><td>registrationDeadline</td><td>String</td><td>报名截止时间</td><td>是</td></tr>
            <tr><td>最大报名人数</td><td>maxParticipants</td><td>Int</td><td>最大报名人数</td><td>是</td></tr>
            <tr><td>活动状态</td><td>status</td><td>String</td><td>未发布、待开始、进行中、已作废、已结束</td><td>是</td></tr>
            <tr><td>联系人</td><td>contactPerson</td><td>String</td><td>联系人</td><td>是</td></tr>
            <tr><td>联系电话</td><td>contactPhone</td><td>String</td><td>联系电话</td><td>是</td></tr>
            <tr><td>备注</td><td>remarks</td><td>String</td><td>备注信息</td><td>是</td></tr>
            <tr><td>创建时间</td><td>createdTime</td><td>String</td><td>创建时间</td><td>是</td></tr>
            <tr><td>更新时间</td><td>updatedTime</td><td>String</td><td>更新时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "title": "春季摄影大赛",
        "categoryId": 1,
        "categoryName": "摄影比赛",
        "description": "展示春天美景的摄影比赛",
        "images": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
        "location": "市中心公园",
        "startTime": "2024-04-01 09:00:00",
        "endTime": "2024-04-01 17:00:00",
        "registrationDeadline": "2024-03-25 23:59:59",
        "maxParticipants": 100,
        "status": "进行中",
        "contactPerson": "张三",
        "contactPhone": "13800138000",
        "remarks": "请准备相机设备",
        "createdTime": "2024-02-01 10:00:00",
        "updatedTime": "2024-02-01 10:00:00"
      }
    ]
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>新增活动内容接口</h3>

        <h4>接口功能</h4>
        <p>管理后台新增活动内容。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>新增活动内容</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityContent/add</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>是</td></tr>
            <tr><td>活动描述</td><td>description</td><td>String</td><td>活动描述</td><td>是</td></tr>
            <tr><td>活动图片</td><td>images</td><td>String</td><td>活动图片URL，多个用逗号分隔</td><td>是</td></tr>
            <tr><td>活动地点</td><td>location</td><td>String</td><td>活动地点</td><td>是</td></tr>
            <tr><td>开始时间</td><td>startTime</td><td>String</td><td>活动开始时间，格式：yyyy-MM-dd HH:mm:ss</td><td>是</td></tr>
            <tr><td>结束时间</td><td>endTime</td><td>String</td><td>活动结束时间，格式：yyyy-MM-dd HH:mm:ss</td><td>是</td></tr>
            <tr><td>报名截止时间</td><td>registrationDeadline</td><td>String</td><td>报名截止时间，格式：yyyy-MM-dd HH:mm:ss</td><td>是</td></tr>
            <tr><td>最大报名人数</td><td>maxParticipants</td><td>Int</td><td>最大报名人数</td><td>是</td></tr>
            <tr><td>联系人</td><td>contactPerson</td><td>String</td><td>联系人</td><td>是</td></tr>
            <tr><td>联系电话</td><td>contactPhone</td><td>String</td><td>联系电话</td><td>是</td></tr>
            <tr><td>备注</td><td>remarks</td><td>String</td><td>备注信息</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "title": "春季摄影大赛",
  "categoryId": 1,
  "description": "展示春天美景的摄影比赛",
  "images": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
  "location": "市中心公园",
  "startTime": "2024-04-01 09:00:00",
  "endTime": "2024-04-01 17:00:00",
  "registrationDeadline": "2024-03-25 23:59:59",
  "maxParticipants": 100,
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "remarks": "请准备相机设备"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>修改活动内容接口</h3>

        <h4>接口功能</h4>
        <p>管理后台修改活动内容信息。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>修改活动内容信息</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityContent/update</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>是</td></tr>
            <tr><td>活动描述</td><td>description</td><td>String</td><td>活动描述</td><td>是</td></tr>
            <tr><td>活动图片</td><td>images</td><td>String</td><td>活动图片URL，多个用逗号分隔</td><td>是</td></tr>
            <tr><td>活动地点</td><td>location</td><td>String</td><td>活动地点</td><td>是</td></tr>
            <tr><td>开始时间</td><td>startTime</td><td>String</td><td>活动开始时间，格式：yyyy-MM-dd HH:mm:ss</td><td>是</td></tr>
            <tr><td>结束时间</td><td>endTime</td><td>String</td><td>活动结束时间，格式：yyyy-MM-dd HH:mm:ss</td><td>是</td></tr>
            <tr><td>报名截止时间</td><td>registrationDeadline</td><td>String</td><td>报名截止时间，格式：yyyy-MM-dd HH:mm:ss</td><td>是</td></tr>
            <tr><td>最大报名人数</td><td>maxParticipants</td><td>Int</td><td>最大报名人数</td><td>是</td></tr>
            <tr><td>联系人</td><td>contactPerson</td><td>String</td><td>联系人</td><td>是</td></tr>
            <tr><td>联系电话</td><td>contactPhone</td><td>String</td><td>联系电话</td><td>是</td></tr>
            <tr><td>备注</td><td>remarks</td><td>String</td><td>备注信息</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1,
  "title": "春季摄影大赛",
  "categoryId": 1,
  "description": "展示春天美景的摄影比赛",
  "images": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
  "location": "市中心公园",
  "startTime": "2024-04-01 09:00:00",
  "endTime": "2024-04-01 17:00:00",
  "registrationDeadline": "2024-03-25 23:59:59",
  "maxParticipants": 100,
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "remarks": "请准备相机设备"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>删除活动内容接口</h3>

        <h4>接口功能</h4>
        <p>管理后台删除活动内容，只有未发布状态的活动才能删除。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>删除活动内容，只有未发布状态的活动才能删除</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityContent/delete</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>活动内容详情接口</h3>

        <h4>接口功能</h4>
        <p>管理后台查询活动内容详情信息。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>查询活动内容详情信息</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityContent/detail</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>活动内容详情</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动描述</td><td>description</td><td>String</td><td>活动描述</td><td>是</td></tr>
            <tr><td>活动图片</td><td>images</td><td>String</td><td>活动图片URL，多个用逗号分隔</td><td>是</td></tr>
            <tr><td>活动地点</td><td>location</td><td>String</td><td>活动地点</td><td>是</td></tr>
            <tr><td>开始时间</td><td>startTime</td><td>String</td><td>活动开始时间</td><td>是</td></tr>
            <tr><td>结束时间</td><td>endTime</td><td>String</td><td>活动结束时间</td><td>是</td></tr>
            <tr><td>报名截止时间</td><td>registrationDeadline</td><td>String</td><td>报名截止时间</td><td>是</td></tr>
            <tr><td>最大报名人数</td><td>maxParticipants</td><td>Int</td><td>最大报名人数</td><td>是</td></tr>
            <tr><td>活动状态</td><td>status</td><td>String</td><td>未发布、待开始、进行中、已作废、已结束</td><td>是</td></tr>
            <tr><td>联系人</td><td>contactPerson</td><td>String</td><td>联系人</td><td>是</td></tr>
            <tr><td>联系电话</td><td>contactPhone</td><td>String</td><td>联系电话</td><td>是</td></tr>
            <tr><td>备注</td><td>remarks</td><td>String</td><td>备注信息</td><td>是</td></tr>
            <tr><td>创建时间</td><td>createdTime</td><td>String</td><td>创建时间</td><td>是</td></tr>
            <tr><td>更新时间</td><td>updatedTime</td><td>String</td><td>更新时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "title": "春季摄影大赛",
    "categoryId": 1,
    "categoryName": "摄影比赛",
    "description": "展示春天美景的摄影比赛",
    "images": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
    "location": "市中心公园",
    "startTime": "2024-04-01 09:00:00",
    "endTime": "2024-04-01 17:00:00",
    "registrationDeadline": "2024-03-25 23:59:59",
    "maxParticipants": 100,
    "status": "进行中",
    "contactPerson": "张三",
    "contactPhone": "13800138000",
    "remarks": "请准备相机设备",
    "createdTime": "2024-02-01 10:00:00",
    "updatedTime": "2024-02-01 10:00:00"
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <h2>用户报名管理模块</h2>

    <div class="interface-section">
        <h3>分页查询用户报名接口</h3>

        <h4>接口功能</h4>
        <p>管理后台分页查询用户报名列表，支持按活动ID、用户信息和时间范围筛选。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>分页查询用户报名列表，支持按活动ID、用户信息和时间范围筛选</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityRegistration/page</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>页码</td><td>pageNum</td><td>Int</td><td>默认1</td><td>否</td></tr>
            <tr><td>页大小</td><td>pageSize</td><td>Int</td><td>默认10</td><td>否</td></tr>
            <tr><td>活动内容ID</td><td>activityId</td><td>Int</td><td>活动内容ID</td><td>否</td></tr>
            <tr><td>用户ID</td><td>userId</td><td>String</td><td>用户ID，支持模糊查询</td><td>否</td></tr>
            <tr><td>用户姓名</td><td>userName</td><td>String</td><td>用户姓名，支持模糊查询</td><td>否</td></tr>
            <tr><td>用户手机号</td><td>userPhone</td><td>String</td><td>用户手机号，支持模糊查询</td><td>否</td></tr>
            <tr><td>报名开始时间</td><td>startTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
            <tr><td>报名结束时间</td><td>endTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "pageNum": 1,
  "pageSize": 10,
  "activityId": 1,
  "userId": "user",
  "userName": "张",
  "userPhone": "138",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>分页数据</td><td>是</td></tr>
            <tr><td>总记录数</td><td>total</td><td>Long</td><td>总记录数</td><td>是</td></tr>
            <tr><td>数据列表</td><td>records</td><td>Array</td><td>用户报名列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动内容ID</td><td>activityId</td><td>Int</td><td>活动内容ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>activityTitle</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>用户ID</td><td>userId</td><td>String</td><td>用户ID</td><td>是</td></tr>
            <tr><td>用户姓名</td><td>userName</td><td>String</td><td>用户姓名</td><td>是</td></tr>
            <tr><td>用户手机号</td><td>userPhone</td><td>String</td><td>用户手机号</td><td>是</td></tr>
            <tr><td>报名数据JSON</td><td>registrationData</td><td>String</td><td>报名数据JSON</td><td>是</td></tr>
            <tr><td>报名时间</td><td>registrationTime</td><td>String</td><td>报名时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "activityId": 1,
        "activityTitle": "春季摄影大赛",
        "userId": "user123",
        "userName": "张三",
        "userPhone": "13800138000",
        "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/work1.jpg\"}",
        "registrationTime": "2024-03-15 14:30:00"
      }
    ]
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>用户报名详情接口</h3>

        <h4>接口功能</h4>
        <p>管理后台查询用户报名详情信息。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>查询用户报名详情信息</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityRegistration/detail</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>用户报名详情</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动内容ID</td><td>activityId</td><td>Int</td><td>活动内容ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>activityTitle</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>用户ID</td><td>userId</td><td>String</td><td>用户ID</td><td>是</td></tr>
            <tr><td>用户姓名</td><td>userName</td><td>String</td><td>用户姓名</td><td>是</td></tr>
            <tr><td>用户手机号</td><td>userPhone</td><td>String</td><td>用户手机号</td><td>是</td></tr>
            <tr><td>报名数据JSON</td><td>registrationData</td><td>String</td><td>报名数据JSON</td><td>是</td></tr>
            <tr><td>报名时间</td><td>registrationTime</td><td>String</td><td>报名时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "activityId": 1,
    "activityTitle": "春季摄影大赛",
    "userId": "user123",
    "userName": "张三",
    "userPhone": "13800138000",
    "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/work1.jpg\"}",
    "registrationTime": "2024-03-15 14:30:00"
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <h2>小程序端接口模块</h2>

    <div class="interface-section">
        <h3>分页查询活动列表接口</h3>

        <h4>接口功能</h4>
        <p>小程序端分页查询活动列表，支持按类别筛选，只显示已发布的活动。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>分页查询活动列表，支持按类别筛选，只显示已发布的活动</td></tr>
            <tr><td>接口提供者</td><td>mini-app-api</td></tr>
            <tr><td>接口调用者</td><td>小程序前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-api/activity/page</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>页码</td><td>pageNum</td><td>Int</td><td>默认1</td><td>否</td></tr>
            <tr><td>页大小</td><td>pageSize</td><td>Int</td><td>默认10</td><td>否</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryId": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>分页数据</td><td>是</td></tr>
            <tr><td>总记录数</td><td>total</td><td>Long</td><td>总记录数</td><td>是</td></tr>
            <tr><td>数据列表</td><td>records</td><td>Array</td><td>活动列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动描述</td><td>description</td><td>String</td><td>活动描述</td><td>是</td></tr>
            <tr><td>活动图片</td><td>images</td><td>String</td><td>活动图片URL，多个用逗号分隔</td><td>是</td></tr>
            <tr><td>活动地点</td><td>location</td><td>String</td><td>活动地点</td><td>是</td></tr>
            <tr><td>开始时间</td><td>startTime</td><td>String</td><td>活动开始时间</td><td>是</td></tr>
            <tr><td>结束时间</td><td>endTime</td><td>String</td><td>活动结束时间</td><td>是</td></tr>
            <tr><td>报名截止时间</td><td>registrationDeadline</td><td>String</td><td>报名截止时间</td><td>是</td></tr>
            <tr><td>最大报名人数</td><td>maxParticipants</td><td>Int</td><td>最大报名人数</td><td>是</td></tr>
            <tr><td>活动状态</td><td>status</td><td>String</td><td>待开始、进行中、已结束</td><td>是</td></tr>
            <tr><td>联系人</td><td>contactPerson</td><td>String</td><td>联系人</td><td>是</td></tr>
            <tr><td>联系电话</td><td>contactPhone</td><td>String</td><td>联系电话</td><td>是</td></tr>
            <tr><td>报名人数</td><td>registrationCount</td><td>Int</td><td>当前报名人数</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "title": "春季摄影大赛",
        "categoryId": 1,
        "categoryName": "摄影比赛",
        "description": "展示春天美景的摄影比赛",
        "images": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
        "location": "市中心公园",
        "startTime": "2024-04-01 09:00:00",
        "endTime": "2024-04-01 17:00:00",
        "registrationDeadline": "2024-03-25 23:59:59",
        "maxParticipants": 100,
        "status": "进行中",
        "contactPerson": "张三",
        "contactPhone": "13800138000",
        "registrationCount": 25
      }
    ]
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>活动详情接口</h3>

        <h4>接口功能</h4>
        <p>小程序端查询活动详情信息，包含表单配置。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>查询活动详情信息，包含表单配置</td></tr>
            <tr><td>接口提供者</td><td>mini-app-api</td></tr>
            <tr><td>接口调用者</td><td>小程序前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-api/activity/detail</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>活动详情</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动标题</td><td>title</td><td>String</td><td>活动标题</td><td>是</td></tr>
            <tr><td>活动类别ID</td><td>categoryId</td><td>Int</td><td>活动类别ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动描述</td><td>description</td><td>String</td><td>活动描述</td><td>是</td></tr>
            <tr><td>活动图片</td><td>images</td><td>String</td><td>活动图片URL，多个用逗号分隔</td><td>是</td></tr>
            <tr><td>活动地点</td><td>location</td><td>String</td><td>活动地点</td><td>是</td></tr>
            <tr><td>开始时间</td><td>startTime</td><td>String</td><td>活动开始时间</td><td>是</td></tr>
            <tr><td>结束时间</td><td>endTime</td><td>String</td><td>活动结束时间</td><td>是</td></tr>
            <tr><td>报名截止时间</td><td>registrationDeadline</td><td>String</td><td>报名截止时间</td><td>是</td></tr>
            <tr><td>最大报名人数</td><td>maxParticipants</td><td>Int</td><td>最大报名人数</td><td>是</td></tr>
            <tr><td>活动状态</td><td>status</td><td>String</td><td>待开始、进行中、已结束</td><td>是</td></tr>
            <tr><td>联系人</td><td>contactPerson</td><td>String</td><td>联系人</td><td>是</td></tr>
            <tr><td>联系电话</td><td>contactPhone</td><td>String</td><td>联系电话</td><td>是</td></tr>
            <tr><td>备注</td><td>remarks</td><td>String</td><td>备注信息</td><td>是</td></tr>
            <tr><td>报名人数</td><td>registrationCount</td><td>Int</td><td>当前报名人数</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "title": "春季摄影大赛",
    "categoryId": 1,
    "categoryName": "摄影比赛",
    "description": "展示春天美景的摄影比赛",
    "images": "http://minio.example.com/activity1.jpg,http://minio.example.com/activity2.jpg",
    "location": "市中心公园",
    "startTime": "2024-04-01 09:00:00",
    "endTime": "2024-04-01 17:00:00",
    "registrationDeadline": "2024-03-25 23:59:59",
    "maxParticipants": 100,
    "status": "进行中",
    "contactPerson": "张三",
    "contactPhone": "13800138000",
    "remarks": "请准备相机设备",
    "registrationCount": 25,
    "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]"
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>活动类别列表接口</h3>

        <h4>接口功能</h4>
        <p>小程序端获取所有活动类别列表，用于筛选。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>获取所有活动类别列表，用于筛选</td></tr>
            <tr><td>接口提供者</td><td>mini-app-api</td></tr>
            <tr><td>接口调用者</td><td>小程序前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-api/activityCategory/list</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <p>无请求参数</p>

        <p>请求示例：</p>
        <pre><code>{}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Array</td><td>活动类别列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryName": "摄影比赛"
    },
    {
      "id": 2,
      "categoryName": "文艺演出"
    }
  ]
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>用户报名接口</h3>

        <h4>接口功能</h4>
        <p>小程序端用户报名参加活动。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>用户报名参加活动</td></tr>
            <tr><td>接口提供者</td><td>mini-app-api</td></tr>
            <tr><td>接口调用者</td><td>小程序前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-api/activityRegistration/register</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>活动内容ID</td><td>activityId</td><td>Int</td><td>活动内容ID</td><td>是</td></tr>
            <tr><td>用户ID</td><td>userId</td><td>String</td><td>用户ID</td><td>是</td></tr>
            <tr><td>用户姓名</td><td>userName</td><td>String</td><td>用户姓名</td><td>是</td></tr>
            <tr><td>用户手机号</td><td>userPhone</td><td>String</td><td>用户手机号</td><td>是</td></tr>
            <tr><td>报名数据JSON</td><td>registrationData</td><td>String</td><td>报名数据JSON</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "activityId": 1,
  "userId": "user123",
  "userName": "张三",
  "userPhone": "13800138000",
  "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/work1.jpg\"}"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "报名成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>查询用户报名状态接口</h3>

        <h4>接口功能</h4>
        <p>小程序端查询用户是否已报名某个活动。</p>

        <h4>调用规则</h4>
        <table>
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>查询用户是否已报名某个活动</td></tr>
            <tr><td>接口提供者</td><td>mini-app-api</td></tr>
            <tr><td>接口调用者</td><td>小程序前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-api/activityRegistration/status</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>活动内容ID</td><td>activityId</td><td>Int</td><td>活动内容ID</td><td>是</td></tr>
            <tr><td>用户ID</td><td>userId</td><td>String</td><td>用户ID</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "activityId": 1,
  "userId": "user123"
}</code></pre>

        <h4>应答参数</h4>
        <table>
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>报名状态信息</td><td>是</td></tr>
            <tr><td>是否已报名</td><td>isRegistered</td><td>Boolean</td><td>是否已报名</td><td>是</td></tr>
            <tr><td>报名时间</td><td>registrationTime</td><td>String</td><td>报名时间，未报名时为null</td><td>否</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "isRegistered": true,
    "registrationTime": "2024-03-15 14:30:00"
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <p style="margin-top: 50px; text-align: center; color: #666; font-size: 12pt;">
        <strong>活动管理系统接口设计文档完成</strong><br>
        共包含15个接口，已删除所有序号便于Word复制使用
    </p>

</body>
</html>
