<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动管理系统接口设计文档</title>
<style>
        body {
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt;
            line-height: 1.5;
            margin: 40px;
            color: #000;
        }
        
        h1 {
            font-family: "宋体", SimSun, serif;
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        h2 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt;
            font-weight: bold;
            margin: 15px 0 10px 0;
        }
        
        h3, h4, h5 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
            margin: 15px 0 10px 0;
        }
        
        /* 简化的表格样式，专为Word复制优化 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            table-layout: auto;
        }

        th, td {
            border: 1px solid #000;
            padding: 5px;
            text-align: left;
            vertical-align: top;
            font-size: 10.5pt;
            word-wrap: break-word;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        /* 两列表格样式 */
        .two-column-table th:first-child,
        .two-column-table td:first-child {
            width: 20%;
            white-space: nowrap;
        }
        
        .two-column-table th:last-child,
        .two-column-table td:last-child {
            width: 80%;
        }
        
        /* 五列表格样式 */
        .five-column-table th:nth-child(1),
        .five-column-table td:nth-child(1) {
            width: 15%;
        }
        
        .five-column-table th:nth-child(2),
        .five-column-table td:nth-child(2) {
            width: 15%;
        }
        
        .five-column-table th:nth-child(3),
        .five-column-table td:nth-child(3) {
            width: 10%;
        }
        
        .five-column-table th:nth-child(4),
        .five-column-table td:nth-child(4) {
            width: 50%;
        }
        
        .five-column-table th:nth-child(5),
        .five-column-table td:nth-child(5) {
            width: 10%;
        }
        
        pre {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: "Courier New", monospace;
            font-size: 9pt;
            margin: 10px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        code {
            font-family: "Courier New", monospace;
            font-size: 9pt;
            background-color: #f5f5f5;
            padding: 2px 4px;
        }
        
        p {
            margin: 8px 0;
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt;
        }

        .interface-section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <h1>活动管理系统接口设计文档</h1>

    <h2>活动类别管理模块</h2>

    <div class="interface-section">
        <h3>分页查询活动类别接口</h3>
        
        <h4>接口功能</h4>
        <p>管理后台分页查询活动类别列表，支持按名称和时间范围筛选。</p>

        <h4>调用规则</h4>
        <table class="two-column-table">
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>分页查询活动类别列表，支持按名称和时间范围筛选</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/page</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table class="five-column-table">
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>页码</td><td>pageNum</td><td>Int</td><td>默认1</td><td>否</td></tr>
            <tr><td>页大小</td><td>pageSize</td><td>Int</td><td>默认10</td><td>否</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>支持模糊查询</td><td>否</td></tr>
            <tr><td>创建开始时间</td><td>startTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
            <tr><td>创建结束时间</td><td>endTime</td><td>String</td><td>格式：yyyy-MM-dd HH:mm:ss</td><td>否</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryName": "摄影",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</code></pre>

        <h4>应答参数</h4>
        <table class="five-column-table">
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
            <tr><td>数据</td><td>data</td><td>Object</td><td>分页数据</td><td>是</td></tr>
            <tr><td>总记录数</td><td>total</td><td>Long</td><td>总记录数</td><td>是</td></tr>
            <tr><td>数据列表</td><td>records</td><td>Array</td><td>活动类别列表</td><td>是</td></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON</td><td>是</td></tr>
            <tr><td>创建时间</td><td>createdTime</td><td>String</td><td>创建时间</td><td>是</td></tr>
            <tr><td>更新时间</td><td>updatedTime</td><td>String</td><td>更新时间</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryName": "摄影比赛",
        "categoryDesc": "各类摄影比赛活动",
        "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>新增活动类别接口</h3>
        
        <h4>接口功能</h4>
        <p>管理后台新增活动类别。</p>

        <h4>调用规则</h4>
        <table class="two-column-table">
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>新增活动类别</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/add</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table class="five-column-table">
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50},{\"fieldName\":\"作品图片\",\"required\":true,\"inputType\":\"upload\",\"maxCount\":3}]"
}</code></pre>

        <h4>应答参数</h4>
        <table class="five-column-table">
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <div class="interface-section">
        <h3>修改活动类别接口</h3>

        <h4>接口功能</h4>
        <p>管理后台修改活动类别信息。</p>

        <h4>调用规则</h4>
        <table class="two-column-table">
            <tr><th>设计项</th><th>说明</th></tr>
            <tr><td>功能说明</td><td>修改活动类别信息</td></tr>
            <tr><td>接口提供者</td><td>mini-app-admin</td></tr>
            <tr><td>接口调用者</td><td>管理后台前端</td></tr>
            <tr><td>协议</td><td>http POST</td></tr>
            <tr><td>请求头</td><td>Content-Type: application/json</td></tr>
            <tr><td>请求路径</td><td>http://{ip}:{port}/mini-app-admin/activityCategory/update</td></tr>
            <tr><td>超时时长</td><td>5000ms</td></tr>
            <tr><td>请求报文</td><td>请求体： json字符串</td></tr>
            <tr><td>响应报文</td><td>响应体： json字符串</td></tr>
        </table>

        <h4>请求参数</h4>
        <table class="five-column-table">
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>主键ID</td><td>id</td><td>Int</td><td>主键ID</td><td>是</td></tr>
            <tr><td>活动类别名称</td><td>categoryName</td><td>String</td><td>活动类别名称</td><td>是</td></tr>
            <tr><td>活动类别描述</td><td>categoryDesc</td><td>String</td><td>活动类别描述</td><td>是</td></tr>
            <tr><td>表单配置JSON</td><td>formConfig</td><td>String</td><td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td><td>是</td></tr>
        </table>

        <p>请求示例：</p>
        <pre><code>{
  "id": 1,
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]"
}</code></pre>

        <h4>应答参数</h4>
        <table class="five-column-table">
            <tr><th>参数名称</th><th>参数命名</th><th>类型</th><th>备注</th><th>是否必填</th></tr>
            <tr><td>状态码</td><td>resultCode</td><td>String</td><td>业务响应码</td><td>是</td></tr>
            <tr><td>提示</td><td>resultMsg</td><td>String</td><td>业务响应信息</td><td>是</td></tr>
        </table>

        <p>响应示例：</p>
        <pre><code>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</code></pre>

        <h4>处理流程</h4>
        <p>无</p>
    </div>

    <!-- 注意：由于篇幅限制，这里只展示了部分接口，完整版本请参考原文档 -->
    <p style="text-align: center; margin-top: 50px; font-style: italic; color: #666;">
        注：本文档为Word复制优化版本，包含了主要接口示例。<br>
        完整版本请参考原始HTML文档。
    </p>

</body>
</html>
