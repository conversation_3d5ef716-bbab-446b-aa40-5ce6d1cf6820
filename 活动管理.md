帮我写接口文档，数据库文档

### 管理后台端
这里有两个子功能，活动类别管理和活动内容管理。

#### 活动类别管理

管理员可配置活动类别信息（分页查询，新增，修改，删除，详情查询）

##### 新增/修改

页面有如下字段：字段中文名，是否必填、输入框类型（输入框/文件/时间组件/日期组件），输入长度，这样我用户就可以自定义每种类别的活动有哪些，报名用户需要填写的字段（比如姓名、电话，作品，表演项目、表演时间、备注，限制人数）了。

##### 详情查询接口

##### 删除

已经有绑定子活动的活动类别不支持删除。

#### 活动内容管理

管理员可配置活动内容（新增，修改，删除，发布，结束，分页查询，详情查询），新增/修改页面有如下字段：活动类别，活动开始时间，活动结束时间，活动名称，活动介绍，活动图片url，活动状态（00-未发布，10-待开始，20-进行中，30-已作废，40-已结束），已报名人数，活动还在待开始状态，就直接点击了结束，状态就是已作废。

在【已发布】和【已结束】状态的数据后面，可以点击查看用户报名的报名数据，这里是调用分页查询报名数据接口。

##### 分页查询接口

分页查询条件：活动类别，活动开始时间，活动结束时间，活动名称，活动状态

##### 状态修改接口(状态传不同的值来区分是结束和发布)

##### 分页查询用户报名数据接口

##### 详情查询接口

##### 删除

只有未发布状态的数据才支持删除。

### 小程序端

活动列表查询：

1. 查询出【未发布】、【已作废】状态以外的数据，包含字段：活动类别，活动开始时间，活动结束时间，活动名称，活动介绍，活动图片url，活动状态；
2. 查询活动类别数据，为活动类别中配置的数据，字段中文名，是否必填、输入框类型（输入框/文件/时间组件/日期组件），输入长度；



##### 提交报名信息接口

提交的参数为活动类别中配置的字段，如果是图片，则接收逗号分割的字符串的图片url；



##### 个人历史报名数据列表

查询个人所有的报名所填写的活动数据；

##### 个人指定活动的详情查询接口

用户点击某个已经报名的活动或者历史参加的活动，查询在该次活动中填写的报名数据。

### 公共

下面是管理后台所有的表都要有的字段，createdBy和updatedBy 如果是中间的关联关系表则不需要。

    @ApiModelProperty(value = "Id")
    private Integer id;
    
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    
    @ApiModelProperty(value = "更新人")
    private String updatedBy;
    
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
    
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;



可能有这些表，活动类别表，活动内容表，用户报名数据表
图片或者文件的url支持多个，存的时候，就是逗号分割的字符串