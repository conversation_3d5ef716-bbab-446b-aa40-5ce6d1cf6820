#### *********** ***\*解除绑定接口\****

##### *********.1** ***\*接口功能\****

港铁小程序用户手动进行解绑操作时，小程序需要通知到后台。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                 |
| ---------------- | ----------------------------------------------- |
| 功能说明         | 小程序解绑后，需要将信息同步给后台              |
| 接口提供者       | mtr-mini-api                                    |
| 接口调用者       | 港铁小程序前端                                  |
| 协议             | http POST                                       |
| 请求头           | sign                                            |
| 请求路径         | http://{ip}:{port}/mtr-mini-api/identify/unbind |
| 超时时长         | 5000ms                                          |
| 请求报文         | 请求体： json字符串                             |
| 响应报文         | 响应体： json字符串                             |

##### *********.3** ***\*请求参数\****

无

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名                          | 类型   | 备注                             | 是否必填 |
| -------- | --------------------------------- | ------ | -------------------------------- | -------- |
| 状态码   | code                              | Int    | 000000-处理成功，100000-系统错误 | 是       |
| 提示     | msg                               | String | 返回描述                         | 是       |
| 示例     | { "code": 200, "msg": "操作成功"} |        |                                  |          |

##### *********.5** ***\*处理流程\****

无

#### *********** ***\*查询当前用户是否实名\****

##### *********.1** ***\*接口功能\****

港铁小程序判断当前用户是否已经完成实名操作。

##### *********.2** ***\*调用规则\****

| ***\*设计项\**** |                                                         |
| ---------------- | ------------------------------------------------------- |
| 功能说明         | 港铁小程序判断当前用户是否已经完成实名操作。            |
| 接口提供者       | mtr-mini-api                                            |
| 接口调用者       | 港铁小程序前端                                          |
| 协议             | http POST                                               |
| 请求头           | sign                                                    |
| 请求路径         | http://{ip}:{port}/mtr-mini-api/identify/isRealNameAuth |
| 超时时长         | 5000ms                                                  |
| 请求报文         | 请求体： json字符串                                     |
| 响应报文         | 响应体： json字符串                                     |

##### *********.3** ***\*请求参数\****

| 参数名称     | 参数命名                                                     | 类型   | 备注 | 是否必填 |
| ------------ | ------------------------------------------------------------ | ------ | ---- | -------- |
| 常用地址实体 | address                                                      | Objcet |      | 是       |
| 示例         | {  "id": 1,  "type": 0,  "content": "广东省深圳市南山区科技园南区",} |        |      |          |

##### *********.4** ***\*应答参数\****

| 参数名称 | 参数命名                          | 类型   | 备注                                                | 是否必填 |
| -------- | --------------------------------- | ------ | --------------------------------------------------- | -------- |
| 状态码   | code                              | Int    | 000000-处理成功，100000-系统错误，110001-乘客未实名 | 是       |
| 提示     | msg                               | String | 返回描述                                            | 是       |
| 示例     | { "code": 200, "msg": "操作成功"} |        |                                                     |          |

##### *********.5** ***\*处理流程\****

无